"use client";
import React, { useRef, useState, useEffect } from "react";
import { motion, useScroll, useMotionValueEvent } from "framer-motion";
import { cn } from "@/lib/utils";

export const StickyScroll = ({
    content,
    contentClassName,
}: {
    content: {
        title: string;
        description: string;
        content?: React.ReactNode | any;
    }[];
    contentClassName?: string;
}) => {
    const [activeCard, setActiveCard] = useState(0);
    const ref = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // Calculate heights for proper scrolling
    const totalCards = content.length;

    // Use simpler scroll tracking
    const { scrollYProgress } = useScroll({
        container: ref,
    });

    // Update active card based on scroll position
    useMotionValueEvent(scrollYProgress, "change", (latest) => {
        // Ensure latest is between 0 and 1
        const progress = Math.min(Math.max(latest, 0), 1);

        // Calculate which card should be active based on scroll position
        const newActiveCard = Math.min(
            Math.floor(progress * totalCards),
            totalCards - 1
        );

        if (newActiveCard !== activeCard) {
            setActiveCard(newActiveCard);
        }
    });

    // Ensure the container has enough height for scrolling
    useEffect(() => {
        if (containerRef.current && ref.current && totalCards > 0) {
            // Set the container height to ensure proper scrolling
            const containerHeight = ref.current.clientHeight;
            containerRef.current.style.height = `${containerHeight * totalCards}px`;
        }
    }, [totalCards]);

    return (
        <motion.div
            className="relative h-[500px] overflow-y-auto bg-transparent"
            ref={ref}
        >
            <div
                ref={containerRef}
                className="absolute top-0 left-0 w-full"
            >
                <div className="sticky top-0 flex h-[500px] items-center justify-center">
                    <div className="grid w-full max-w-6xl grid-cols-1 gap-4 px-4 md:grid-cols-2 md:gap-10">
                        {/* Left side - Text content */}
                        <div className="flex flex-col justify-center">
                            <div className="relative">
                                {content.map((item, index) => (
                                    <motion.div
                                        key={`text-${index}`}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{
                                            opacity: activeCard === index ? 1 : 0,
                                            y: activeCard === index ? 0 : 20,
                                        }}
                                        transition={{
                                            duration: 0.5,
                                            ease: "easeInOut",
                                        }}
                                        className={`absolute top-0 left-0 w-full ${
                                            activeCard === index ? "relative" : ""
                                        }`}
                                    >
                                        <h2 className="text-2xl font-bold text-white mb-4">
                                            {item.title}
                                        </h2>
                                        <p className="text-neutral-300">
                                            {item.description}
                                        </p>
                                    </motion.div>
                                ))}
                            </div>
                        </div>

                        {/* Right side - Visual content */}
                        <div className="flex items-center justify-center">
                            <div className="relative h-60 w-full overflow-hidden rounded-md">
                                {content.map((item, index) => (
                                    <motion.div
                                        key={`content-${index}`}
                                        initial={{ opacity: 0 }}
                                        animate={{
                                            opacity: activeCard === index ? 1 : 0,
                                        }}
                                        transition={{
                                            duration: 0.5,
                                            ease: "easeInOut",
                                        }}
                                        className={`absolute inset-0 ${
                                            activeCard === index ? "z-10" : "z-0"
                                        }`}
                                    >
                                        {/* Just render the content directly */}
                                        {item.content}
                                    </motion.div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    );
};
