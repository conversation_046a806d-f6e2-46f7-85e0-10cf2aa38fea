"use client";

import React from 'react'
import {z } from 'zod'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { Button } from "@/components/ui/button"
import { toast } from "sonner"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { addxHandle } from '@/server/waitingList';

const formSchema = z.object({
    xHandle: z.string(),
    })

export default function RegisterWl(){
    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
          xHandle: "",
        },
    })
     
    async function onSubmit(values: z.infer<typeof formSchema>) {
        const {success, message} = await addxHandle(values.xHandle)
        
        if(success){
            form.reset()
            toast(message)    
        }else{
            toast.error(message)
        }
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-row gap-4 justify-center relative z-20 w-full px-4 sm:px-0">
                <FormField
                    control={form.control}
                    name="xHandle"
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                                <Input placeholder="X handle (Web3Tools_fun)" {...field} className="w-full min-w-[160px] sm:min-w-[240px]" />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />
                <Button type="submit">Submit</Button>
            </form>
        </Form>
    )
  }


