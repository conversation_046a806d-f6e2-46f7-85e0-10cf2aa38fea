import { NextRequest } from 'next/server';
import { getTokenRequirementsF<PERSON><PERSON>hain } from '@/app/utils/systemSettings';

type Context = {
  params: Promise<{
    chainId: string;
  }>;
};

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { chainId } = await context.params;

    if (!chainId) {
      return Response.json(
        { error: 'Chain ID is required' },
        { status: 400 }
      );
    }

    // Get token requirements for the specified chain
    const requirements = await getTokenRequirementsForChain(chainId);
    
    return Response.json(requirements, {
      headers: {
        'Cache-Control': 'public, max-age=60, stale-while-revalidate=300',
      }
    });
  } catch (error) {
    console.error('Failed to fetch token requirements:', error);
    return Response.json(
      { error: 'Failed to fetch token requirements' },
      { status: 500 }
    );
  }
}
