import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const profileName = searchParams.get('name');
    const walletAddress = searchParams.get('walletAddress');

    if (!profileName) {
      console.log('Profile name is missing');
      return Response.json(
        { error: 'Profile name is required' },
        { status: 400 }
      );
    }

    console.log(`Checking ownership for profile: ${profileName} with wallet: ${walletAddress || 'not connected'}`);

    if (!walletAddress) {
      console.log('Wallet address is missing');
      return Response.json(
        { isOwner: false, message: 'Wallet not connected' },
        { status: 200 }
      );
    }

    // Get profile by name
    const profile = await db
      .select({
        address: web3Profile.address,
        name: web3Profile.name,
        status: web3Profile.status
      })
      .from(web3Profile)
      .where(eq(web3Profile.name, profileName));

    console.log(`Profile lookup by name '${profileName}' results:`, profile);

    // If profile not found by name, try by address
    if (profile.length === 0 && profileName.startsWith('0x')) {
      console.log(`Profile not found by name, trying by address: ${profileName}`);
      const profileByAddress = await db
        .select({
          address: web3Profile.address,
          name: web3Profile.name,
          status: web3Profile.status
        })
        .from(web3Profile)
        .where(eq(web3Profile.address, profileName));

      console.log(`Profile lookup by address results:`, profileByAddress);

      if (profileByAddress.length > 0) {
        const isOwner = profileByAddress[0].address.toLowerCase() === walletAddress.toLowerCase();
        console.log(`Profile found by address. Is owner: ${isOwner}`);
        return Response.json({
          isOwner,
          profileStatus: profileByAddress[0].status,
          message: isOwner ? 'You are the owner of this profile' : 'You are not the owner of this profile'
        });
      }
    }

    // If profile not found
    if (profile.length === 0) {
      console.log(`Profile not found: ${profileName}`);
      return Response.json(
        { isOwner: false, message: 'Profile not found' },
        { status: 200 }
      );
    }

    // Check if wallet address matches profile address
    const isOwner = profile[0].address.toLowerCase() === walletAddress.toLowerCase();
    console.log(`Profile found. Profile address: ${profile[0].address}, Wallet address: ${walletAddress}`);
    console.log(`Is owner: ${isOwner}`);

    return Response.json({
      isOwner,
      profileStatus: profile[0].status,
      message: isOwner ? 'You are the owner of this profile' : 'You are not the owner of this profile'
    });
  } catch (error) {
    console.error('Failed to check profile ownership:', error);
    return Response.json(
      { error: 'Failed to check profile ownership' },
      { status: 500 }
    );
  }
}
