import { db } from '@/db/drizzle';
import { componentImages } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { randomUUID } from 'crypto';
import { base64ToDataUrl } from './imageUtils';

/**
 * Save or update image positioning in the componentImages table
 * @param address User's wallet address
 * @param componentType Component type (banner, profilePicture, hero)
 * @param section Section identifier (e.g., hero section index)
 * @param metadata Positioning metadata for the image
 * @returns True if successful
 */
export async function saveImagePositioning(
  address: string,
  componentType: string,
  section: string = '0',
  metadata: {
    scale?: number | string;
    positionX?: number;
    positionY?: number;
    naturalWidth?: number | null;
    naturalHeight?: number | null;
  }
): Promise<boolean> {
  try {
    // Check if an image record exists
    const existingImages = await db
      .select()
      .from(componentImages)
      .where(
        and(
          eq(componentImages.address, address),
          eq(componentImages.componentType, componentType),
          eq(componentImages.section, section)
        )
      );

    if (existingImages.length > 0) {
      // Update existing image record
      await db.update(componentImages)
        .set({
          scale: metadata.scale?.toString() || '1',
          positionX: metadata.positionX || 0,
          positionY: metadata.positionY || 0,
          naturalWidth: metadata.naturalWidth,
          naturalHeight: metadata.naturalHeight,
          updatedAt: new Date()
        })
        .where(
          and(
            eq(componentImages.address, address),
            eq(componentImages.componentType, componentType),
            eq(componentImages.section, section)
          )
        );
    } else {
      // Create a placeholder record with positioning data but no image
      const id = randomUUID();
      await db.insert(componentImages).values({
        id,
        address,
        componentType,
        section,
        imageData: null, // No image data yet
        scale: metadata.scale?.toString() || '1',
        positionX: metadata.positionX || 0,
        positionY: metadata.positionY || 0,
        naturalWidth: metadata.naturalWidth,
        naturalHeight: metadata.naturalHeight,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    return true;
  } catch (error) {
    console.error(`Error saving image positioning for ${address}/${componentType}/${section}:`, error);
    return false;
  }
}

/**
 * Save an image to the componentImages table
 * @param address User's wallet address
 * @param componentType Component type (banner, profilePicture, hero)
 * @param section Section identifier (e.g., hero section index)
 * @param imageData Base64 encoded image data
 * @param metadata Additional metadata for the image
 * @returns The ID of the saved image
 */
export async function saveImage(
  address: string,
  componentType: string,
  section: string = '0',
  imageData: string,
  metadata: {
    scale?: number | string;
    positionX?: number;
    positionY?: number;
    naturalWidth?: number;
    naturalHeight?: number;
  } = {}
): Promise<string> {
  try {
    // Generate a UUID for the image
    const id = randomUUID();

    // Insert the image into the componentImages table
    await db.insert(componentImages).values({
      id,
      address,
      componentType,
      section,
      imageData,
      scale: metadata.scale?.toString() || '1',
      positionX: metadata.positionX || 0,
      positionY: metadata.positionY || 0,
      naturalWidth: metadata.naturalWidth || null,
      naturalHeight: metadata.naturalHeight || null,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    return id;
  } catch (error) {
    console.error('Error saving image:', error);
    throw error;
  }
}

/**
 * Get an image from the componentImages table
 * @param id Image ID
 * @returns The image data and metadata
 */
export async function getImageById(id: string): Promise<any> {
  try {
    const image = await db
      .select()
      .from(componentImages)
      .where(eq(componentImages.id, id));

    if (image.length === 0) {
      return null;
    }

    return image[0];
  } catch (error) {
    console.error('Error getting image by ID:', error);
    throw error;
  }
}

/**
 * Get images for a component
 * @param address User's wallet address
 * @param componentType Component type (banner, profilePicture, hero)
 * @param section Optional section identifier
 * @returns Array of images
 */
export async function getComponentImages(
  address: string,
  componentType: string,
  section?: string
): Promise<any[]> {
  try {
    let query = db
      .select()
      .from(componentImages)
      .where(
        section
          ? and(
              eq(componentImages.address, address),
              eq(componentImages.componentType, componentType),
              eq(componentImages.section, section)
            )
          : and(
              eq(componentImages.address, address),
              eq(componentImages.componentType, componentType)
            )
      );

    return await query;
  } catch (error) {
    console.error('Error getting component images:', error);
    throw error;
  }
}

/**
 * Delete an image from the componentImages table
 * @param id Image ID
 * @returns Boolean indicating success
 */
export async function deleteImage(id: string): Promise<boolean> {
  try {
    await db
      .delete(componentImages)
      .where(eq(componentImages.id, id));

    return true;
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
}

/**
 * Update an image in the componentImages table
 * @param id Image ID
 * @param updates Updates to apply
 * @returns Boolean indicating success
 */
export async function updateImage(
  id: string,
  updates: {
    imageData?: string;
    scale?: number | string;
    positionX?: number;
    positionY?: number;
    naturalWidth?: number;
    naturalHeight?: number;
  }
): Promise<boolean> {
  try {
    // Convert scale to string if it's a number
    const processedUpdates = {
      ...updates,
      scale: updates.scale !== undefined ? String(updates.scale) : undefined
    };

    await db
      .update(componentImages)
      .set({
        ...processedUpdates,
        updatedAt: new Date()
      })
      .where(eq(componentImages.id, id));

    return true;
  } catch (error) {
    console.error('Error updating image:', error);
    throw error;
  }
}

/**
 * Get image data URL by ID
 * @param id Image ID
 * @returns Data URL for the image
 */
export async function getImageDataUrl(id: string): Promise<string | null> {
  try {
    const image = await getImageById(id);

    if (!image || !image.imageData) {
      return null;
    }

    return base64ToDataUrl(image.imageData);
  } catch (error) {
    console.error('Error getting image data URL:', error);
    throw error;
  }
}

/**
 * Delete all images for a component
 * @param address User's wallet address
 * @param componentType Component type (banner, profilePicture, hero)
 * @param section Optional section identifier
 * @returns Boolean indicating success
 */
export async function deleteComponentImages(
  address: string,
  componentType: string,
  section?: string
): Promise<boolean> {
  try {
    let query = db
      .delete(componentImages)
      .where(
        section
          ? and(
              eq(componentImages.address, address),
              eq(componentImages.componentType, componentType),
              eq(componentImages.section, section)
            )
          : and(
              eq(componentImages.address, address),
              eq(componentImages.componentType, componentType)
            )
      );

    await query;

    return true;
  } catch (error) {
    console.error('Error deleting component images:', error);
    throw error;
  }
}
