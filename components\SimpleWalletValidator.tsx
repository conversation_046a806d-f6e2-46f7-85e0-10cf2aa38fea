'use client';

import { ReactNode, useEffect } from 'react';
import { useAccount } from 'wagmi';
import Image from 'next/image';

interface SimpleWalletValidatorProps {
  children: ReactNode;
  fallbackContent?: ReactNode;
}

// A simplified version of WalletValidator that only checks if the wallet is connected
// without checking the profile status
export default function SimpleWalletValidator({
  children,
  fallbackContent
}: SimpleWalletValidatorProps) {
  const { isConnected } = useAccount();

  // Check for wallet reconnection on component mount and when isConnected changes
  useEffect(() => {
    // Check if we were previously connected (from localStorage)
    const wasConnected = localStorage.getItem('walletConnected') === 'true';

    if (wasConnected && !isConnected) {
      console.log('[SimpleWalletValidator] Wallet was previously connected but is now disconnected');

      // Attempt to trigger reconnection by dispatching a custom event
      const reconnectEvent = new CustomEvent('wallet-reconnect-needed');
      window.dispatchEvent(reconnectEvent);
    }
  }, [isConnected]);

  // Empty fallback content
  const emptyFallbackContent = (
    <div className="mt-8 max-w-md mx-auto">
      <h2 className="text-lg font-semibold mb-4">Wallet Connection Required</h2>
      <div className="bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md p-4 mb-4">
        Please connect your wallet to continue.
      </div>
    </div>
  );

  return (
    <>
      {isConnected ? children : (fallbackContent || emptyFallbackContent)}
    </>
  );
}
