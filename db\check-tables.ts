import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

async function checkTables() {
  try {
    console.log('Checking if tables exist...');

    // Check if systemSettings table exists
    const systemSettingsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'system_settings'
    `);
    console.log('systemSettings table exists:', (systemSettingsExists as any)[0][0].count > 0);

    // Check if web3Profile table exists
    const web3ProfileExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'web3Profile'
    `);
    console.log('web3Profile table exists:', (web3ProfileExists as any)[0][0].count > 0);

    // Check if waitingList table exists
    const waitingListExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'waitingList'
    `);
    console.log('waitingList table exists:', (waitingListExists as any)[0][0].count > 0);

    // Check if componentPositions table exists
    const componentPositionsExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'componentPositions'
    `);
    console.log('componentPositions table exists:', (componentPositionsExists as any)[0][0].count > 0);

    // Check if componentImages table exists
    const componentImagesExists = await db.execute(sql`
      SELECT COUNT(*) as count FROM information_schema.tables
      WHERE table_schema = DATABASE() AND table_name = 'componentImages'
    `);
    console.log('componentImages table exists:', (componentImagesExists as any)[0][0].count > 0);
  } catch (error) {
    console.error('Error checking tables:', error);
    process.exit(1);
  }
}

// Run the check function
checkTables()
  .then(() => {
    console.log('Table check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during table check:', error);
    process.exit(1);
  });
