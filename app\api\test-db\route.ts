import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { profileLikes } from '@/db/schema';
import { sql } from 'drizzle-orm';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    // Test database connection
    const result = await db.execute(sql`SELECT 1 as test`);
    
    // Test profileLikes table
    let profileLikesExists = true;
    try {
      await db.execute(sql`SELECT 1 FROM profileLikes LIMIT 1`);
    } catch (error) {
      profileLikesExists = false;
    }
    
    return Response.json({
      success: true,
      dbConnection: 'working',
      profileLikesTable: profileLikesExists ? 'exists' : 'does not exist',
      result
    });
  } catch (error) {
    console.error('Database test failed:', error);
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
