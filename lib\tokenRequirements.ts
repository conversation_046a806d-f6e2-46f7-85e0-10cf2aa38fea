"use client";

// Client-side utility to fetch token requirements

interface TokenRequirement {
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
  burnAmount: string;
}

// Cache for token requirements to avoid repeated fetches
let tokenRequirementsCache: Record<string, TokenRequirement> = {};

/**
 * Fetch token requirements for a specific chain
 * @param chainId The chain ID to get requirements for
 * @returns Promise with token requirements
 */
export async function fetchTokenRequirements(chainId: string): Promise<TokenRequirement> {
  // Return from cache if available
  if (tokenRequirementsCache[chainId]) {
    return tokenRequirementsCache[chainId];
  }

  try {
    // Fetch token requirements from API
    const response = await fetch(`/api/token-requirements/${chainId}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch token requirements: ${response.statusText}`);
    }

    const data = await response.json();

    // Cache the result
    tokenRequirementsCache[chainId] = data;

    return data;
  } catch (error) {
    console.error(`Error fetching token requirements for chain ${chainId}:`, error);

    // Return default values on error
    return {
      tokenAddress: chainId === '25' ? '0xe68892c424E4f0EDE343F6F05c873F7b7a528048' : '',
      tokenName: 'Web3Tools',
      minimumHoldings: '0',
      burnAmount: '0'
    };
  }
}

/**
 * Clear the token requirements cache
 */
export function clearTokenRequirementsCache() {
  tokenRequirementsCache = {};
}
