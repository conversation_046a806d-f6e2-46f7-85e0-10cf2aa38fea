"use client";

// Cache for profile status to avoid repeated fetches
let profileStatusCache: Record<string, {
  status: string;
  isApproved: boolean;
  address?: string;
  name?: string;
  transactionHash?: string;
  expiryDate?: Date;
}> = {};

/**
 * Fetch profile status from the database
 * @param addressOrName Address or name of the profile to check
 * @returns Promise with profile status
 */
export async function checkProfileStatus(addressOrName: string): Promise<{
  status: string;
  isApproved: boolean;
  message: string;
  address?: string;
  name?: string;
  transactionHash?: string;
  expiryDate?: Date;
}> {
  // Return from cache if available
  if (profileStatusCache[addressOrName]) {
    const cached = profileStatusCache[addressOrName];
    return {
      ...cached,
      message: getStatusMessage(cached.status)
    };
  }

  try {
    // Fetch profile status from API
    const response = await fetch(`/api/profile/check-status?identifier=${addressOrName}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch profile status: ${response.statusText}`);
    }

    const data = await response.json();

    // Cache the result
    profileStatusCache[addressOrName] = {
      status: data.status,
      isApproved: data.status === 'approved',
      address: data.address,
      name: data.name,
      transactionHash: data.transactionHash,
      expiryDate: data.expiryDate
    };

    return {
      status: data.status,
      isApproved: data.status === 'approved',
      message: getStatusMessage(data.status),
      address: data.address,
      name: data.name,
      transactionHash: data.transactionHash,
      expiryDate: data.expiryDate
    };
  } catch (error) {
    console.error(`Error fetching profile status for ${addressOrName}:`, error);

    // Return default values on error
    return {
      status: 'error',
      isApproved: false,
      message: 'Error checking profile status. Please try again later.'
    };
  }
}

/**
 * Get a user-friendly message based on profile status
 */
function getStatusMessage(status: string): string {
  switch (status) {
    case 'approved':
      return 'Profile is approved and accessible.';
    case 'new':
      return 'This profile is new and waiting for approval. Please burn tokens and provide a transaction hash for verification.';
    case 'in-progress':
      return 'This profile is being processed. Please check back later.';
    case 'pending':
      return 'This profile is pending approval. Please check back later.';
    case 'deleted':
      return 'This profile has been deleted.';
    case 'expired':
      return 'This profile has expired. Please contact an admin to renew it.';
    default:
      return 'Profile status is unknown.';
  }
}

/**
 * Clear the profile status cache
 */
export function clearProfileStatusCache() {
  profileStatusCache = {};
}
