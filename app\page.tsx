import RegisterWl from "@/components/RegisterWl";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import TrueFocus from "@/components/ui/TrueFocus";
import GlitchText from "@/components/ui/GlitchText";
import DecryptedText from "@/components/ui/DecryptedText";
import Web3ToolsProfileRenderer from "./components/Web3ToolsProfileRenderer";

export default function Home() {
  return (
    <main className="min-h-screen">
      <div className="container mx-auto px-4 pt-2 pb-8">
        <div className="text-center mb-6">
          <div className="h-16 sm:h-20 md:h-24 flex items-center justify-center">
            <TrueFocus
              sentence="Create Arrange View"
              manualMode={false}
              blurAmount={5}
              borderColor="#3b82f6" // Blue color
              glowColor="rgba(59, 130, 246, 0.6)" // Blue glow
              animationDuration={2}
              pauseBetweenAnimations={1}
            />
          </div>

          <div className="mt-10">
            <div className="flex flex-col items-center">
              <div className="flex flex-col items-end">
                <GlitchText
                  speed={0.7}
                  enableShadows={true}
                  enableOnHover={false}
                  className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-wide inline-block"
                >
                  Web3Socials
                </GlitchText>

                <div className="mt-2">
                  <DecryptedText
                    text="Your Socials, On-chain..."
                    speed={80}
                    maxIterations={15}
                    sequential={true}
                    revealDirection="start"
                    animateOn="view"
                    className="text-sm md:text-base text-neutral-400 font-light tracking-wider italic"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Web3Tools Profile Section */}
        <Web3ToolsProfileRenderer />
      </div>
    </main>
  );
}
