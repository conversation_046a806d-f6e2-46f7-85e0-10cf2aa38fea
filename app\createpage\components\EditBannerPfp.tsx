'use client';

import { useState, useEffect, useRef, useReducer } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Upload, ZoomIn, ZoomOut, RefreshCw, Circle, Square, SquareDashed, Loader2, Save } from 'lucide-react';
import { HeaderTypewriterEffect } from '@/components/ui/header-typewriter-effect';
import ColorPicker from './ColorPicker';
import FontColorPicker from './FontColorPicker';
import ComponentVisibilityToggle from './ComponentVisibilityToggle';

interface BannerPfpState {
  bannerUrl: string | null;
  bannerScale: number;
  bannerPosition: { x: number; y: number };
  bannerNaturalSize: { width: number; height: number } | null;
  profileUrl: string | null;
  profileScale: number;
  profilePosition: { x: number; y: number };
  profileNaturalSize: { width: number; height: number } | null;
  profileShape: 'circular' | 'rectangular' | 'squarish';
  profileHorizontalPosition: number;
  profileName?: string;
  profileBio?: string;
  urlName?: string; // New field for URL name
  profileNameHorizontalPosition: number;
  profileNameStyle: {
    fontSize: string;
    fontWeight: string;
    fontColor: string;
    effect: 'none' | 'typewriter';
  };
}

interface EditBannerPfpProps {
  bannerPfpState: BannerPfpState;
  setBannerPfpState: (state: BannerPfpState | ((prevState: BannerPfpState) => BannerPfpState)) => void;
  address: string;
  hidden: string;
}

export default function EditBannerPfp({
  bannerPfpState,
  setBannerPfpState,
  address,
  hidden
}: EditBannerPfpProps) {
  const bannerContainerRef = useRef<HTMLDivElement>(null);
  const profileContainerRef = useRef<HTMLDivElement>(null);
  const bannerInputRef = useRef<HTMLInputElement>(null);
  const profileInputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef<number>(0);
  const startPositionRef = useRef<number>(50);
  const currentPositionRef = useRef<number>(50);

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isHidden, setIsHidden] = useState(hidden === 'Y');

  // Force update function to trigger re-renders without state updates
  const [, forceUpdate] = useReducer(x => x + 1, 0);

  // Initialize refs with bannerPfpState values
  useEffect(() => {
    if (bannerPfpState) {
      if (bannerPfpState.profileHorizontalPosition !== undefined) {
        currentPositionRef.current = bannerPfpState.profileHorizontalPosition;
      }
      if (bannerPfpState.bannerPosition) {
        bannerPositionRef.current = bannerPfpState.bannerPosition;
      }
      if (bannerPfpState.profileNameHorizontalPosition !== undefined) {
        profileNamePositionRef.current = bannerPfpState.profileNameHorizontalPosition;
      }
    }
  }, [bannerPfpState]);

  // Initialize scales when component mounts
  useEffect(() => {
    // Ensure banner scale is initialized
    if (bannerPfpState.bannerUrl && (!bannerPfpState.bannerScale || bannerPfpState.bannerScale <= 0)) {
      setBannerPfpState(prev => ({
        ...prev,
        bannerScale: 1
      }));
    }

    // Ensure profile scale is initialized
    if (bannerPfpState.profileUrl && (!bannerPfpState.profileScale || bannerPfpState.profileScale <= 0)) {
      setBannerPfpState(prev => ({
        ...prev,
        profileScale: 1
      }));
    }
  }, [bannerPfpState.bannerUrl, bannerPfpState.profileUrl]);
  const [backgroundColor, setBackgroundColor] = useState('transparent');
  const [isDraggingBanner, setIsDraggingBanner] = useState(false);
  const [isDraggingProfile, setIsDraggingProfile] = useState(false);
  const [isDraggingProfileHorizontal, setIsDraggingProfileHorizontal] = useState(false);
  const [isDraggingProfileName, setIsDraggingProfileName] = useState(false);
  const profileNamePositionRef = useRef<number>(50);

  // Use a ref to track if this is the first render
  const isFirstRender = useRef(true);

  // Load banner and profile picture metadata
  useEffect(() => {
    // Skip loading if no address is provided
    if (!address) {
      console.log('No address provided, skipping bannerpfp metadata load');
      return;
    }

    // Skip if not the first render
    if (!isFirstRender.current) {
      return;
    }

    // Mark as not first render
    isFirstRender.current = false;

    const loadBannerPfpMetadata = async () => {
      // Add retry logic
      let retries = 3;
      let success = false;
      let data = null;

      while (retries > 0 && !success) {
        try {
          console.log(`Attempting to fetch bannerpfp metadata for ${address}, retries left: ${retries}`);
          // Fetch the bannerpfp metadata directly without calling ensure-defaults
          // The parent component should have already ensured defaults
          const response = await fetch(`/api/bannerpfp/${address}`, {
            // Add cache control to prevent caching issues
            cache: 'no-store',
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to load banner and profile picture metadata: ${response.status} ${response.statusText}`);
          }

          data = await response.json();
          console.log('Loaded bannerpfp metadata:', data);
          console.log('URL name from API:', data.urlName);
          success = true;
        } catch (error) {
          console.error(`Attempt ${4 - retries} failed:`, error);
          retries--;
          if (retries > 0) {
            // Wait before retrying
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      if (!success || !data) {
        console.error('All attempts to load bannerpfp metadata failed');
        return;
      }

      // Update state with available data
      setBannerPfpState(prev => ({
        ...prev,
        bannerUrl: data.bannerUrl || prev.bannerUrl,
        bannerScale: data.bannerScale || prev.bannerScale,
        bannerPosition: data.bannerPosition || prev.bannerPosition,
        bannerNaturalSize: data.bannerNaturalSize || prev.bannerNaturalSize,
        profileUrl: data.profileUrl || prev.profileUrl,
        profileScale: data.profileScale || prev.profileScale,
        profilePosition: data.profilePosition || prev.profilePosition,
        profileNaturalSize: data.profileNaturalSize || prev.profileNaturalSize,
        profileShape: data.profileShape || prev.profileShape || 'circular',
        profileHorizontalPosition: data.profileHorizontalPosition !== undefined ? data.profileHorizontalPosition : 50,
        profileName: data.profileName || prev.profileName || '',
        profileBio: data.profileBio || prev.profileBio || '',
        urlName: data.urlName || prev.urlName || '',
        profileNameHorizontalPosition: data.profileNameHorizontalPosition !== undefined ? data.profileNameHorizontalPosition : 50,
        profileNameStyle: data.profileNameStyle || prev.profileNameStyle || {
          fontSize: '1.5rem',
          fontWeight: 'bold',
          fontColor: '#ffffff',
          effect: 'typewriter'
        }
      }));

      // If no images found, log it but don't refresh
      if (!data.bannerUrl || !data.profileUrl) {
        console.log('No banner or profile picture found for bannerpfp component');
      }
    };

    // Call the function to load metadata
    loadBannerPfpMetadata();
  }, [address]); // Only run when address changes

  // Handle visibility change
  const handleVisibilityChange = (componentType: string, newHidden: string) => {
    setIsHidden(newHidden === 'Y');
  };

  // Handle banner upload
  const handleBannerUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsSaving(true);
      // Store the current banner URL before uploading new one
      const previousUrl = bannerPfpState.bannerUrl;

      // Upload new banner first
      const fileExtension = file.name.split('.').pop() || 'jpg';
      const filename = `${address}_bannerpfp_banner.${fileExtension}`;

      const response = await fetch(`/api/upload?filename=${encodeURIComponent(filename)}`, {
        method: 'POST',
        body: file
      });

      if (!response.ok) throw new Error('Failed to upload banner');

      const data = await response.json();
      console.log('Banner upload response:', data);

      // Update state with new banner URL
      setBannerPfpState(prev => ({
        ...prev,
        bannerUrl: data.url,
        bannerScale: 1,
        bannerPosition: { x: 0, y: 0 },
        bannerNaturalSize: null
      }));

      setHasUnsavedChanges(true);
      toast.success('Banner uploaded successfully');
    } catch (error) {
      console.error('Failed to upload banner:', error);
      toast.error('Failed to upload banner');
    } finally {
      setIsSaving(false);
    }
  };

  // Handle profile picture upload
  const handleProfileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsSaving(true);
      // Store the current profile URL before uploading new one
      const previousUrl = bannerPfpState.profileUrl;

      // Upload new profile picture
      const fileExtension = file.name.split('.').pop() || 'jpg';
      const filename = `${address}_bannerpfp_profile.${fileExtension}`;

      const response = await fetch(`/api/upload?filename=${encodeURIComponent(filename)}`, {
        method: 'POST',
        body: file
      });

      if (!response.ok) throw new Error('Failed to upload profile picture');

      const data = await response.json();
      console.log('Profile picture upload response:', data);

      // Update state with new profile URL
      setBannerPfpState(prev => ({
        ...prev,
        profileUrl: data.url,
        profileScale: 1,
        profilePosition: { x: 0, y: 0 },
        profileNaturalSize: null
      }));

      setHasUnsavedChanges(true);
      toast.success('Profile picture uploaded successfully');
    } catch (error) {
      console.error('Failed to upload profile picture:', error);
      toast.error('Failed to upload profile picture');
    } finally {
      setIsSaving(false);
    }
  };

  // Create refs for banner position
  const bannerPositionRef = useRef({ x: 0, y: 0 });

  // Handle banner mouse down for dragging
  const handleBannerMouseDown = (e: React.MouseEvent) => {
    if (!containerRef.current) return;

    e.preventDefault();
    setIsDraggingBanner(true);

    const startX = e.clientX;
    const startY = e.clientY;
    const startPositionX = bannerPfpState.bannerPosition.x;
    const startPositionY = bannerPfpState.bannerPosition.y;

    // Initialize the banner position ref
    bannerPositionRef.current = {
      x: startPositionX,
      y: startPositionY
    };

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaY = moveEvent.clientY - startY;

      // Update the ref instead of the state
      bannerPositionRef.current = {
        x: startPositionX + deltaX,
        y: startPositionY + deltaY
      };

      // Force a re-render without state update
      forceUpdate();
    };

    const handleMouseUp = () => {
      setIsDraggingBanner(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Update the state only once at the end of dragging
      setBannerPfpState(prev => ({
        ...prev,
        bannerPosition: bannerPositionRef.current
      }));
      setHasUnsavedChanges(true);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle profile picture mouse down for dragging
  const handleProfileMouseDown = (e: React.MouseEvent) => {
    if (!profileContainerRef.current) return;

    e.preventDefault();
    setIsDraggingProfile(true);

    const startX = e.clientX;
    const startY = e.clientY;
    const startPositionX = bannerPfpState.profilePosition.x;
    const startPositionY = bannerPfpState.profilePosition.y;

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaY = moveEvent.clientY - startY;

      setBannerPfpState(prev => ({
        ...prev,
        profilePosition: {
          x: startPositionX + deltaX,
          y: startPositionY + deltaY
        }
      }));
      setHasUnsavedChanges(true);
    };

    const handleMouseUp = () => {
      setIsDraggingProfile(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Calculate position based on mouse/touch event
  const calculatePosition = (e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement> | MouseEvent | TouchEvent): number => {
    if (!containerRef.current) return 50;

    // Get container dimensions
    const rect = containerRef.current.getBoundingClientRect();
    const containerWidth = rect.width;

    // Get the x position relative to the container
    let clientX;
    if ('touches' in e) {
      // Touch event
      clientX = e.touches[0].clientX;
    } else if ('clientX' in e) {
      // Mouse event
      clientX = e.clientX;
    } else {
      return 50; // Default if we can't determine position
    }

    // Calculate position as percentage
    const offsetX = clientX - rect.left;
    const percentage = (offsetX / containerWidth) * 100;

    // Constrain between 10% and 90%
    return Math.max(10, Math.min(90, percentage));
  };

  // Update the state with the current position (called on mouse up)
  const updatePositionState = () => {
    setBannerPfpState(prev => ({
      ...prev,
      profileHorizontalPosition: currentPositionRef.current
    }));
    setHasUnsavedChanges(true);
  };

  // Update the state with the current profile name position (called on mouse up)
  const updateProfileNamePositionState = async () => {
    // Update local state
    setBannerPfpState(prev => ({
      ...prev,
      profileNameHorizontalPosition: profileNamePositionRef.current
    }));
    setHasUnsavedChanges(true);

    // Save the position to the server immediately
    try {
      const response = await fetch(`/api/bannerpfp/${address}/profilename-position`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profileNameHorizontalPosition: profileNamePositionRef.current
        }),
      });

      if (!response.ok) {
        console.error('Failed to save profile name position');
      } else {
        console.log('Profile name position saved successfully');
      }
    } catch (error) {
      console.error('Error saving profile name position:', error);
    }
  };

  // Handle mouse down for horizontal profile picture dragging
  const handleProfileHorizontalMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    // Set dragging state
    setIsDraggingProfileHorizontal(true);

    // Initial position calculation
    currentPositionRef.current = calculatePosition(e);

    // Set up mouse move handler
    const handleMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new position
      currentPositionRef.current = calculatePosition(moveEvent);

      // Force a re-render without state update
      forceUpdate();
    };

    // Set up mouse up handler
    const handleMouseUp = () => {
      setIsDraggingProfileHorizontal(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Update state only once at the end of dragging
      updatePositionState();
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle mouse down for profile name dragging
  const handleProfileNameMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    // Set dragging state
    setIsDraggingProfileName(true);

    // Initial position calculation
    profileNamePositionRef.current = calculatePosition(e);

    // Set up mouse move handler
    const handleMouseMove = (moveEvent: MouseEvent) => {
      // Calculate new position
      profileNamePositionRef.current = calculatePosition(moveEvent);

      // Force a re-render without state update
      forceUpdate();
    };

    // Set up mouse up handler
    const handleMouseUp = () => {
      setIsDraggingProfileName(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // Update state only once at the end of dragging
      updateProfileNamePositionState();
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle touch start for profile name dragging
  const handleProfileNameTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    // Set dragging state
    setIsDraggingProfileName(true);

    // Initial position calculation
    profileNamePositionRef.current = calculatePosition(e);

    // Set up touch move handler
    const handleTouchMove = (moveEvent: TouchEvent) => {
      if (moveEvent.cancelable) {
        moveEvent.preventDefault(); // Prevent scrolling only if cancelable
      }

      // Calculate new position
      profileNamePositionRef.current = calculatePosition(moveEvent);

      // Force a re-render without state update
      forceUpdate();
    };

    // Set up touch end handler
    const handleTouchEnd = () => {
      setIsDraggingProfileName(false);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      // Update state only once at the end of dragging
      updateProfileNamePositionState();
    };

    // Add event listeners
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  };

  // Handle touch start for profile picture dragging
  const handleProfileTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    // Set dragging state
    setIsDraggingProfileHorizontal(true);

    // Initial position calculation
    currentPositionRef.current = calculatePosition(e);

    // Set up touch move handler
    const handleTouchMove = (moveEvent: TouchEvent) => {
      if (moveEvent.cancelable) {
        moveEvent.preventDefault(); // Prevent scrolling only if cancelable
      }

      // Calculate new position
      currentPositionRef.current = calculatePosition(moveEvent);

      // Force a re-render without state update
      forceUpdate();
    };

    // Set up touch end handler
    const handleTouchEnd = () => {
      setIsDraggingProfileHorizontal(false);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleTouchEnd);

      // Update state only once at the end of dragging
      updatePositionState();
    };

    // Add event listeners
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  };

  // Handle banner scale change
  const handleBannerScaleChange = (delta: number) => {
    setBannerPfpState(prev => {
      // Ensure we have a valid scale value to start with
      const currentScale = prev.bannerScale || 1;
      return {
        ...prev,
        bannerScale: Math.max(0.1, currentScale + delta)
      };
    });
    setHasUnsavedChanges(true);
  };

  // Handle profile scale change
  const handleProfileScaleChange = (delta: number) => {
    setBannerPfpState(prev => {
      // Ensure we have a valid scale value to start with
      const currentScale = prev.profileScale || 1;
      return {
        ...prev,
        profileScale: Math.max(0.1, currentScale + delta)
      };
    });
    setHasUnsavedChanges(true);
  };

  // Handle profile shape change
  const handleShapeChange = (shape: 'circular' | 'rectangular' | 'squarish') => {
    setBannerPfpState(prev => ({
      ...prev,
      profileShape: shape
    }));
    setHasUnsavedChanges(true);
  };

  // Handle profile name change
  const handleProfileNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Allow users to clear and retype during editing
    setBannerPfpState(prev => ({
      ...prev,
      profileName: e.target.value
    }));
    setHasUnsavedChanges(true);
  };

  // Handle URL name change
  const handleUrlNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow single word (no spaces) for URL name
    const value = e.target.value.replace(/\s+/g, '');

    // Allow users to clear and retype during editing
    setBannerPfpState(prev => ({
      ...prev,
      urlName: value
    }));
    setHasUnsavedChanges(true);
  };

  // Handle profile name style changes
  const handleProfileNameStyleChange = (property: 'fontSize' | 'fontWeight' | 'fontColor' | 'effect', value: string) => {
    setBannerPfpState(prev => {
      // Create default style if it doesn't exist
      const currentStyle = prev.profileNameStyle || {
        fontSize: '1.5rem',
        fontWeight: 'bold',
        fontColor: '#ffffff',
        effect: 'typewriter'
      };

      return {
        ...prev,
        profileNameStyle: {
          ...currentStyle,
          [property]: value
        }
      };
    });
    setHasUnsavedChanges(true);
  };

  // Font color handler - this will be applied to the profile name
  const setFontColor = (color: string) => {
    console.log('EditBannerPfp received font color:', color);
    // Update the profile name style with the new font color
    handleProfileNameStyleChange('fontColor', color);
    // Force a re-render to ensure the color is applied
    forceUpdate();
  };

  // Save changes
  const handleSaveChanges = async () => {
    // Validate profile name before saving
    if (!bannerPfpState.profileName || bannerPfpState.profileName.trim() === '') {
      toast.error('Profile name is required');
      return;
    }

    // Validate URL name before saving
    if (!bannerPfpState.urlName || bannerPfpState.urlName.trim() === '') {
      toast.error('URL name is required');
      return;
    }

    // Ensure URL name is a single word (no spaces)
    if (bannerPfpState.urlName.includes(' ')) {
      toast.error('URL name must be a single word (no spaces)');
      return;
    }

    // Save changes to the API
    try {
      setIsSaving(true);
      console.log('Saving bannerpfp changes:', bannerPfpState);

      const response = await fetch(`/api/bannerpfp/${address}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bannerUrl: bannerPfpState.bannerUrl,
          bannerScale: bannerPfpState.bannerScale,
          bannerPosition: bannerPfpState.bannerPosition,
          bannerNaturalSize: bannerPfpState.bannerNaturalSize,
          profileUrl: bannerPfpState.profileUrl,
          profileScale: bannerPfpState.profileScale,
          profilePosition: bannerPfpState.profilePosition,
          profileNaturalSize: bannerPfpState.profileNaturalSize,
          profileShape: bannerPfpState.profileShape,
          profileHorizontalPosition: bannerPfpState.profileHorizontalPosition,
          profileName: bannerPfpState.profileName,
          profileBio: bannerPfpState.profileBio,
          urlName: bannerPfpState.urlName,
          profileNameHorizontalPosition: bannerPfpState.profileNameHorizontalPosition,
          profileNameStyle: bannerPfpState.profileNameStyle,
          backgroundColor,
          fontColor: bannerPfpState.profileNameStyle?.fontColor || '#ffffff'
        }),
      });

      if (!response.ok) throw new Error('Failed to save changes');

      setHasUnsavedChanges(false);
      toast.success('Changes saved successfully');
    } catch (error) {
      console.error('Failed to save changes:', error);
      toast.error('Failed to save changes');
    } finally {
      setIsSaving(false);
    }
  };

  // Get border radius class based on shape
  const getBorderRadiusClass = () => {
    switch (bannerPfpState.profileShape) {
      case 'rectangular':
        return 'rounded-none'; // Completely rectangular (no rounded corners)
      case 'squarish':
        return 'rounded-md'; // Slightly rounded corners
      case 'circular':
      default:
        return 'rounded-full';
    }
  };

  return (
    <div className="relative border border-neutral-800 overflow-hidden mt-4 w-full">
      {/* Part 1: Banner, Profile Picture, and Name (with background color) */}

      {/* Visibility Toggle */}
      <div className="absolute top-2 right-2 z-10">
        <ComponentVisibilityToggle
          componentType="bannerpfp"
          address={address}
          initialHidden={hidden}
          onChange={handleVisibilityChange}
          minimal={true}
        />
      </div>

      {/* Color Pickers */}
      <ColorPicker
        address={address}
        componentType="bannerpfp"
        onColorChange={setBackgroundColor}
        chain="25"
      />

      <FontColorPicker
        address={address}
        componentType="bannerpfp"
        onColorChange={setFontColor}
        chain="25"
      />

      {/* Background color container - covers from top to profile name */}
      <div style={{ backgroundColor, width: '100%', minWidth: '100%', boxSizing: 'border-box', paddingBottom: '0.5rem' }} className="w-full min-w-full">
        {/* Banner Section */}
        <div className="p-0 space-y-3 mt-3">
          {/* Banner section - label removed */}

          {/* Container with relative positioning to hold both banner and profile picture */}
          <div className="relative w-full" style={{
            marginBottom: bannerPfpState.profileShape === 'rectangular' ? '4rem' : '3.5rem',
            width: '100%'
          }}>
          {/* Banner container with fixed height */}
          <div
            ref={containerRef}
            className="w-full h-48 md:h-64 relative overflow-hidden"
            style={{ width: '100%', minWidth: '100%' }}
          >
            {/* Banner Preview */}
            {bannerPfpState.bannerUrl ? (
              <div
                className="absolute inset-0"
                style={{
                  backgroundImage: `url(${bannerPfpState.bannerUrl})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  transform: `translate(${isDraggingBanner ? bannerPositionRef.current.x : bannerPfpState.bannerPosition.x}px, ${isDraggingBanner ? bannerPositionRef.current.y : bannerPfpState.bannerPosition.y}px) scale(${bannerPfpState.bannerScale || 1})`,
                  transformOrigin: 'center',
                  cursor: isDraggingBanner ? 'grabbing' : 'grab'
                }}
                onMouseDown={handleBannerMouseDown}
              />
            ) : (
              <div className="w-full h-full bg-neutral-800 flex items-center justify-center">
                <span className="text-neutral-400">No banner image</span>
              </div>
            )}

            {/* Position indicator line for profile picture */}
            <div
              className="absolute bottom-0 w-0.5 h-6 bg-blue-500 opacity-50"
              style={{
                left: `${isDraggingProfileHorizontal ? currentPositionRef.current : bannerPfpState.profileHorizontalPosition}%`,
                transform: 'translateX(-50%)'
              }}
            />

            {/* No position indicator line for profile name in the banner */}
          </div>

          {/* Position indicator - the profile picture itself with percentage overlay */}
          {/* This is shown only during dragging */}
          <div
            className={`absolute ${isDraggingProfileHorizontal ? 'opacity-100' : 'opacity-0'}`}
            style={{
              left: `${isDraggingProfileHorizontal ? currentPositionRef.current : bannerPfpState.profileHorizontalPosition}%`,
              bottom: bannerPfpState.profileShape === 'rectangular' ? '-72px' : '-64px', /* Position so exactly half overlaps the banner */
              transform: `translateX(-50%)`, /* No vertical translation needed */
              zIndex: 20,
              transition: isDraggingProfileHorizontal ? 'none' : 'left 0.1s ease-out',
              pointerEvents: 'none' // Prevent this from interfering with mouse events
            }}
          >
            <div className={`${bannerPfpState.profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32'} overflow-hidden ${getBorderRadiusClass()} relative`}>
              {bannerPfpState.profileUrl ? (
                <img
                  src={bannerPfpState.profileUrl}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-neutral-800 flex items-center justify-center">
                  <span className="text-neutral-400 text-xs">No image</span>
                </div>
              )}
              {/* Percentage overlay - hidden */}
            </div>
          </div>

          {/* The actual profile picture that will be saved */}
          <div
            className={`absolute cursor-move group ${isDraggingProfileHorizontal ? 'opacity-30' : 'opacity-100'}`}
            style={{
              left: `${isDraggingProfileHorizontal ? currentPositionRef.current : bannerPfpState.profileHorizontalPosition}%`,
              bottom: bannerPfpState.profileShape === 'rectangular' ? '-72px' : '-64px', /* Position so exactly half overlaps the banner */
              transform: `translateX(-50%)`, /* No vertical translation needed */
              zIndex: 10,
              transition: isDraggingProfileHorizontal ? 'none' : 'left 0.1s ease-out'
            }}
            onMouseDown={handleProfileHorizontalMouseDown}
            onTouchStart={handleProfileTouchStart}
          >
            {/* Drag handle indicator */}
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              ← Drag to reposition →
            </div>

            {/* Percentage indicator - hidden */}

            <div
              className={`${bannerPfpState.profileShape === 'rectangular' ? 'w-28 h-36' : 'w-32 h-32'} overflow-hidden ${getBorderRadiusClass()} relative cursor-grab ${isDraggingProfileHorizontal ? 'cursor-grabbing' : ''}`}
              style={{ backgroundColor: 'transparent' }}
            >

              {bannerPfpState.profileUrl ? (
                <img
                  src={bannerPfpState.profileUrl}
                  alt="Profile"
                  className="w-full h-full object-cover"
                  style={{
                    transform: `scale(${bannerPfpState.profileScale})`,
                    transformOrigin: 'center',
                  }}
                />
              ) : (
                <div className="w-full h-full bg-neutral-800 flex items-center justify-center">
                  <span className="text-neutral-400 text-xs">No Image</span>
                </div>
              )}
            </div>
          </div>

          {/* Profile name moved to its own section */}
          </div>

          {/* Draggable Profile Name Section - Positioned above controls */}
          <div className="relative w-full" style={{ height: '2rem', marginTop: '-4rem', marginBottom: '3rem' }}>
          {/* Position indicator line for profile name */}
          <div
            className="absolute top-0 w-0.5 h-6 bg-transparent"
            style={{
              left: `${isDraggingProfileName ? profileNamePositionRef.current : bannerPfpState.profileNameHorizontalPosition}%`,
              transform: 'translateX(-50%)'
            }}
          />

          {/* Draggable Profile Name */}
          <div
            className={`absolute cursor-move group ${isDraggingProfileName ? 'opacity-30' : 'opacity-100'}`}
            style={{
              left: `${isDraggingProfileName ? profileNamePositionRef.current : bannerPfpState.profileNameHorizontalPosition}%`,
              top: '0px',
              transform: `translateX(-50%)`,
              zIndex: 10,
              transition: isDraggingProfileName ? 'none' : 'left 0.1s ease-out'
            }}
            onMouseDown={handleProfileNameMouseDown}
            onTouchStart={handleProfileNameTouchStart}
          >
            {/* Drag handle indicator */}
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-black/30 text-white px-2 py-1 rounded text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              ← Drag to reposition →
            </div>

            {/* Profile Name */}
            <div className="text-center px-0 py-0 bg-transparent whitespace-nowrap inline-block">
              {bannerPfpState.profileNameStyle?.effect === 'typewriter' ? (
                <div>
                  <HeaderTypewriterEffect
                    words={[{
                      text: bannerPfpState.profileName || 'Your Name',
                      className: ''
                    }]}
                    className="inline-flex"
                    cursorClassName="text-white"
                    style={{
                      color: bannerPfpState.profileNameStyle?.fontColor || '#ffffff',
                      fontSize: bannerPfpState.profileNameStyle?.fontSize || '1.5rem',
                      fontWeight: bannerPfpState.profileNameStyle?.fontWeight || 'bold',
                      lineHeight: 1
                    }}
                  />
                </div>
              ) : (
                <div style={{
                    fontSize: `calc(${bannerPfpState.profileNameStyle?.fontSize || '1.5rem'} * 1.2)`,
                    fontWeight: bannerPfpState.profileNameStyle?.fontWeight || 'bold',
                    color: bannerPfpState.profileNameStyle?.fontColor || '#ffffff',
                    lineHeight: 1,
                    paddingTop: '1.75rem'
                  }}>
                  {bannerPfpState.profileName || 'Your Name'}
                </div>
              )}
            </div>
          </div>
          </div>
        </div>
      </div>

      {/* Part 2: Controls (without background color) */}
      {/* Controls Container - Reorganized */}
      <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 px-4">
        {/* Left Column - Banner Controls */}
        <div className="space-y-3">
          <h3 className="text-lg font-medium mb-2">Banner Controls</h3>
          <div className="flex flex-wrap gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => bannerInputRef.current?.click()}
              disabled={isSaving}
            >
              <Upload className="h-4 w-4 mr-1" />
              Upload Banner
            </Button>
            <input
              ref={bannerInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleBannerUpload}
              disabled={isSaving}
            />

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBannerScaleChange(0.1)}
              disabled={isSaving}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBannerScaleChange(-0.1)}
              disabled={isSaving}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setBannerPfpState(prev => ({
                  ...prev,
                  bannerScale: 1,
                  bannerPosition: { x: 0, y: 0 }
                }));
                setHasUnsavedChanges(true);
              }}
              disabled={isSaving}
            >
              <RefreshCw className="h-4 w-4" />
              Reset
            </Button>
          </div>
        </div>

        {/* Middle Column - Profile Picture Controls */}
        <div className="space-y-3">
          <h3 className="text-lg font-medium mb-2">Profile Picture Controls</h3>
          {/* Upload Button and Zoom Controls in one line */}
          <div className="flex flex-wrap gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => profileInputRef.current?.click()}
              disabled={isSaving}
            >
              <Upload className="h-4 w-4 mr-1" />
              Upload Profile
            </Button>
            <input
              ref={profileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleProfileUpload}
              disabled={isSaving}
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleProfileScaleChange(0.1)}
              disabled={isSaving}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => handleProfileScaleChange(-0.1)}
              disabled={isSaving}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                setBannerPfpState(prev => ({
                  ...prev,
                  profileScale: 1,
                  profilePosition: { x: 0, y: 0 }
                }));
                setHasUnsavedChanges(true);
              }}
              disabled={isSaving}
            >
              <RefreshCw className="h-4 w-4" />
              Reset
            </Button>
          </div>

          {/* Shape Controls */}
          <div className="flex flex-wrap gap-1">
            <Button
              size="sm"
              variant={bannerPfpState.profileShape === 'circular' ? 'default' : 'outline'}
              onClick={() => handleShapeChange('circular')}
              disabled={isSaving}
            >
              <Circle className="h-4 w-4 mr-1" />
              Circle
            </Button>

            <Button
              size="sm"
              variant={bannerPfpState.profileShape === 'squarish' ? 'default' : 'outline'}
              onClick={() => handleShapeChange('squarish')}
              disabled={isSaving}
            >
              <SquareDashed className="h-4 w-4 mr-1" />
              Soft Square
            </Button>

            <Button
              size="sm"
              variant={bannerPfpState.profileShape === 'rectangular' ? 'default' : 'outline'}
              onClick={() => handleShapeChange('rectangular')}
              disabled={isSaving}
            >
              <Square className="h-4 w-4 mr-1" />
              Rectangle
            </Button>
          </div>
        </div>

        {/* Right Column - Profile Name Controls */}
        <div className="space-y-3">
          <h3 className="text-lg font-medium mb-2">Profile Name</h3>
          <div className="space-y-2">
            {/* Profile Name Input */}
            <div>
              <label htmlFor="profileName" className="block text-sm font-medium mb-1">
                Profile Name <span className="text-red-500">*</span>
              </label>
              <input
                id="profileName"
                type="text"
                value={bannerPfpState.profileName || ''}
                onChange={handleProfileNameChange}
                className="w-full px-3 py-2 bg-neutral-800 border border-neutral-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSaving}
                placeholder="Enter your name"
                required
              />
            </div>

            {/* URL Name Input */}
            <div>
              <label htmlFor="urlName" className="block text-sm font-medium mb-1">
                URL Name <span className="text-red-500">*</span>
                <span className="text-xs text-neutral-400 ml-2">(single word, no spaces)</span>
              </label>
              <input
                id="urlName"
                type="text"
                value={bannerPfpState.urlName || ''}
                onChange={handleUrlNameChange}
                className="w-full px-3 py-2 bg-neutral-800 border border-neutral-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSaving}
                placeholder="Enter URL name (single word)"
                required
              />
            </div>

            {/* Profile Name Style Controls - only effect dropdown */}
            <div>
              <label className="block text-sm font-medium mb-1">
                Name Effect
              </label>
              <select
                id="nameEffect"
                value={bannerPfpState.profileNameStyle?.effect || 'none'}
                onChange={(e) => handleProfileNameStyleChange('effect', e.target.value)}
                className="w-full px-3 py-2 bg-neutral-800 border border-neutral-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={isSaving}
              >
                <option value="typewriter">Typewriter</option>
                <option value="none">None</option>
              </select>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-center mt-4 col-span-1 md:col-span-3 mb-4">
        <Button
          onClick={handleSaveChanges}
          disabled={!hasUnsavedChanges || isSaving}
          className="w-full max-w-xs"
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Profile
            </>
          )}
        </Button>
        </div>
      </div>
    </div>
  );
}
