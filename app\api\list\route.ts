import { NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(_request: Request) {
  try {
    // Get all bannerpfp components from componentPositions
    const bannerpfps = await db.select().from(componentPositions).where(eq(componentPositions.componentType, 'bannerpfp'));

    // Return counts and addresses (but not the actual image data to keep response size reasonable)
    return NextResponse.json({
      bannerpfpCount: bannerpfps.length,
      bannerpfpAddresses: bannerpfps.map(p => p.address)
    });
  } catch (error) {
    // Failed to list images
    return NextResponse.json(
      { error: 'Failed to list images' },
      { status: 500 }
    );
  }
}