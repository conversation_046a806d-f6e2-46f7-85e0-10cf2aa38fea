'use client';

import { useState, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff } from 'lucide-react';
import { toast } from 'sonner';

interface ComponentVisibilityToggleProps {
  componentType: string;
  address: string;
  initialHidden: string; // 'Y' or 'N'
  onChange?: (componentType: string, hidden: string) => void; // Optional callback
  minimal?: boolean; // New prop for minimal version
}

export default function ComponentVisibilityToggle({
  componentType,
  address,
  initialHidden,
  onChange,
  minimal = false
}: ComponentVisibilityToggleProps) {
  const [isVisible, setIsVisible] = useState(initialHidden !== 'Y');
  const [isUpdating, setIsUpdating] = useState(false);

  // Update local state when initialHidden changes
  useEffect(() => {
    setIsVisible(initialHidden !== 'Y');
  }, [initialHidden]);

  const handleToggle = async (checked: boolean) => {
    try {
      setIsUpdating(true);
      const newHidden = checked ? 'N' : 'Y'; // If checked, component is visible (hidden = 'N')

      // Update the component visibility in the database
      const response = await fetch(`/api/component-visibility/${address}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          componentType,
          hidden: newHidden,
          chain: "25" // Include chain ID as required by the API
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update component visibility');
      }

      setIsVisible(checked);

      // Call the onChange callback if provided
      if (onChange) {
        onChange(componentType, newHidden);
      }

      toast.success(`${formatComponentType(componentType)} is now ${checked ? 'visible' : 'hidden'}`);
    } catch (error) {
      console.error('Failed to update component visibility:', error);
      toast.error('Failed to update visibility');
      // Revert the UI state on error
      setIsVisible(initialHidden !== 'Y');
    } finally {
      setIsUpdating(false);
    }
  };

  // Format component type for display
  const formatComponentType = (type: string) => {
    switch (type) {
      case 'banner':
        return 'Banner';
      case 'profilePicture':
        return 'Profile Picture';
      case 'details':
        return 'Details';
      case 'socialLinks':
        return 'Social Links';
      case 'hero':
        return 'Hero';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  if (minimal) {
    return (
      <div className="flex items-center gap-2">
        <button
          onClick={() => handleToggle(!isVisible)}
          disabled={isUpdating}
          className={`flex items-center gap-1.5 px-2 py-1 rounded-full text-xs font-medium transition-colors duration-200 cursor-pointer ${
            isVisible
              ? 'bg-green-900/30 text-green-400 hover:bg-green-900/40'
              : 'bg-red-900/30 text-red-400 hover:bg-red-900/40'
          }`}
          title={`${isVisible ? 'Hide' : 'Show'} ${formatComponentType(componentType)}`}
        >
          {isVisible ? (
            <Eye className="h-3.5 w-3.5" />
          ) : (
            <EyeOff className="h-3.5 w-3.5" />
          )}
          <span>{formatComponentType(componentType)}</span>
        </button>
      </div>
    );
  }

  // Original full version render
  return (
    <div className={`flex items-center justify-between p-3 rounded-md ${
      isVisible ? 'bg-blue-900/30 border border-blue-800/50' : 'bg-black/30 border border-red-900/30'
    } transition-colors duration-200`}>
      <div className="flex items-center space-x-2">
        {isVisible ? (
          <Eye className="h-5 w-5 text-green-400" />
        ) : (
          <EyeOff className="h-5 w-5 text-red-400" />
        )}
        <span className="font-medium text-sm">
          {formatComponentType(componentType)}
        </span>
      </div>

      <div className="flex items-center gap-3">
        <span className={`text-xs px-2 py-1 rounded-full ${
          isVisible ? 'bg-green-900/50 text-green-400' : 'bg-red-900/50 text-red-400'
        }`}>
          {isVisible ? 'Visible' : 'Hidden'}
        </span>
        <Checkbox
          id={`visibility-${componentType}`}
          checked={isVisible}
          onCheckedChange={handleToggle}
          disabled={isUpdating}
          className="h-5 w-5 data-[state=checked]:bg-green-500 data-[state=checked]:text-white"
        />
      </div>
    </div>
  );
}
