import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

type Context = {
  params: Promise<{
    address: string;
  }>;
};

// POST to update profile horizontal position
export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;
    const { profileHorizontalPosition } = await request.json();

    if (profileHorizontalPosition === undefined) {
      return Response.json(
        { error: 'Profile horizontal position is required' },
        { status: 400 }
      );
    }

    // Check if we already have a record for this address
    const existingRecord = await db.select().from(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'bannerpfp')
      )
    );

    if (existingRecord.length > 0) {
      // Get existing details
      const existingDetails = existingRecord[0].details || {};

      // Update existing record
      await db.update(componentPositions)
        .set({
          details: {
            ...existingDetails,
            profileHorizontalPosition
          },
          updatedAt: new Date()
        })
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

      return Response.json({ message: 'Profile position updated successfully' });
    } else {
      return Response.json(
        { error: 'Bannerpfp component not found' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Failed to update profile position:', error);
    return Response.json(
      { error: 'Failed to update profile position' },
      { status: 500 }
    );
  }
}
