import { config } from "dotenv";
import { db } from './drizzle';
import { web3Profile, componentPositions } from './schema';
import { eq, and } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Migration script to add the name field to existing profiles
 * This will:
 * 1. Add the name field to the web3Profile table if it doesn't exist
 * 2. Update existing profiles with the urlName from the bannerpfp component
 * 3. If no urlName exists, use the profileName from the bannerpfp component
 * 4. If no profileName exists, use the first 8 characters of the address
 */
async function migrateNameField() {
  try {
    console.log('Starting migration to add name field to existing profiles...');

    // Check if the name field exists in the web3Profile table
    try {
      console.log('Checking if name field exists in web3Profile table...');
      await db.execute(`SELECT name FROM web3Profile LIMIT 1`);
      console.log('Name field already exists in web3Profile table');
    } catch (error) {
      console.log('Name field does not exist in web3Profile table, adding it...');
      await db.execute(`ALTER TABLE web3Profile ADD COLUMN name VARCHAR(255)`);
      console.log('Name field added to web3Profile table');
    }

    // Get all profiles
    const profiles = await db.select().from(web3Profile);
    console.log(`Found ${profiles.length} profiles to update`);

    // Update each profile with the name field
    for (const profile of profiles) {
      const address = profile.address;
      console.log(`Updating profile for address: ${address}`);

      // Get the bannerpfp component for this address
      const bannerpfpComponents = await db
        .select()
        .from(componentPositions)
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

      if (bannerpfpComponents.length === 0) {
        console.log(`No bannerpfp component found for address: ${address}, using address substring`);
        // Use the first 8 characters of the address as the name
        await db.update(web3Profile)
          .set({
            name: address.substring(0, 8),
            updatedAt: new Date()
          })
          .where(eq(web3Profile.address, address));
        continue;
      }

      const bannerpfp = bannerpfpComponents[0];
      const details: {
        urlName?: string;
        profileName?: string;
      } = bannerpfp.details || {};

      // Use urlName if it exists, otherwise use profileName, otherwise use address substring
      let name = details.urlName || details.profileName || address.substring(0, 8);

      // Ensure the name is a single word (no spaces) and only contains alphanumeric characters
      name = name.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '');
      console.log(`Setting name to: ${name} for address: ${address}`);

      // Update the profile with the name field
      await db.update(web3Profile)
        .set({
          name: name,
          updatedAt: new Date()
        })
        .where(eq(web3Profile.address, address));
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
migrateNameField();
