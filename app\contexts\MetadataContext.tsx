'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface ImageMetadata {
  blobUrl: string;
  scale: number;
  position: { x: number; y: number };
  naturalSize?: { width: number; height: number };
  shape?: string;
}

interface MetadataContextType {
  bannerMetadata: ImageMetadata | null;
  profilePictureMetadata: ImageMetadata | null;
  bannerPfpMetadata: any | null;
  fetchBannerMetadata: (address: string) => Promise<ImageMetadata | null>;
  fetchProfilePictureMetadata: (address: string, compPosition?: string) => Promise<ImageMetadata | null>;
  fetchBannerPfpMetadata: (address: string) => Promise<any | null>;
  clearMetadata: () => void;
}

const MetadataContext = createContext<MetadataContextType | undefined>(undefined);

export function MetadataProvider({ children }: { children: ReactNode }) {
  const [bannerMetadata, setBannerMetadata] = useState<ImageMetadata | null>(null);
  const [profilePictureMetadata, setProfilePictureMetadata] = useState<ImageMetadata | null>(null);
  const [bannerPfpMetadata, setBannerPfpMetadata] = useState<any | null>(null);
  const [currentAddress, setCurrentAddress] = useState<string | null>(null);

  // Function to fetch banner metadata - DEPRECATED
  // Now using bannerpfp component instead
  const fetchBannerMetadata = async (address: string): Promise<ImageMetadata | null> => {
    console.log('Banner component is deprecated, using bannerpfp instead');
    // Try to get banner data from bannerpfp metadata
    const bannerpfpData = await fetchBannerPfpMetadata(address);
    if (bannerpfpData && bannerpfpData.bannerBlobUrl) {
      const completeMetadata = {
        blobUrl: bannerpfpData.bannerBlobUrl,
        scale: bannerpfpData.bannerScale || 1,
        position: { x: 0, y: 0 },
        naturalSize: undefined
      };
      setBannerMetadata(completeMetadata);
      return completeMetadata;
    }
    return null;
  };

  // Function to fetch profile picture metadata - DEPRECATED
  // Now using bannerpfp component instead
  const fetchProfilePictureMetadata = async (address: string, compPosition?: string): Promise<ImageMetadata | null> => {
    console.log('ProfilePicture component is deprecated, using bannerpfp instead');
    // Try to get profile picture data from bannerpfp metadata
    const bannerpfpData = await fetchBannerPfpMetadata(address);
    if (bannerpfpData && bannerpfpData.profileBlobUrl) {
      const completeMetadata = {
        blobUrl: bannerpfpData.profileBlobUrl,
        scale: bannerpfpData.profileScale || 1,
        position: { x: 0, y: 0 },
        naturalSize: undefined,
        shape: bannerpfpData.profileShape || 'circular'
      };
      setProfilePictureMetadata(completeMetadata);
      return completeMetadata;
    }
    return null;
  };

  // Track in-progress fetches to prevent duplicate calls
  const fetchingAddresses = new Set<string>();

  // Function to fetch bannerpfp metadata
  const fetchBannerPfpMetadata = async (address: string): Promise<any | null> => {
    // If we already have metadata for this address, return it
    if (bannerPfpMetadata && currentAddress === address) {
      console.log('MetadataContext: Using cached bannerpfp metadata for', address);
      return bannerPfpMetadata;
    }

    // If we're already fetching this address, wait for it to complete
    if (fetchingAddresses.has(address)) {
      console.log('MetadataContext: Already fetching bannerpfp metadata for', address);
      // Wait a bit and return the current metadata (which should be updated by the in-progress fetch)
      await new Promise(resolve => setTimeout(resolve, 500));
      return bannerPfpMetadata;
    }

    // Mark this address as being fetched
    fetchingAddresses.add(address);

    try {
      console.log('MetadataContext: Fetching bannerpfp metadata for', address);
      const response = await fetch(`/api/bannerpfp/${address}`);

      if (!response.ok) {
        // If the response is not OK, log the error but don't throw
        // This allows the UI to continue rendering even if metadata is missing
        console.warn(`Failed to fetch bannerpfp metadata: ${response.status} ${response.statusText}`);

        // For 404 errors, we'll create a default metadata object
        if (response.status === 404) {
          console.log('Creating default bannerpfp metadata since none exists');
          const defaultMetadata = {
            profileName: address.substring(0, 8),
            profileShape: 'circular',
            profileHorizontalPosition: 50,
            profileNameHorizontalPosition: 50,
            profileNameStyle: 'typewriter'
          };
          setBannerPfpMetadata(defaultMetadata);
          setCurrentAddress(address);
          return defaultMetadata;
        }
        return null;
      }

      const metadata = await response.json();
      console.log('MetadataContext: Received bannerpfp metadata:', metadata);
      if (metadata) {
        console.log('MetadataContext: Setting bannerpfp metadata:', metadata);
        setBannerPfpMetadata(metadata);
        setCurrentAddress(address);
        return metadata;
      }
    } catch (error) {
      console.error('Failed to load bannerpfp metadata:', error);
      // Create a default metadata object on error
      const defaultMetadata = {
        profileName: address.substring(0, 8),
        profileShape: 'circular',
        profileHorizontalPosition: 50,
        profileNameHorizontalPosition: 50,
        profileNameStyle: 'typewriter'
      };
      setBannerPfpMetadata(defaultMetadata);
      setCurrentAddress(address);
      return defaultMetadata;
    } finally {
      // Remove this address from the fetching set
      fetchingAddresses.delete(address);
    }
    return null;
  };

  // Function to clear metadata (useful when changing addresses)
  const clearMetadata = () => {
    setBannerMetadata(null);
    setProfilePictureMetadata(null);
    setBannerPfpMetadata(null);
    setCurrentAddress(null);
  };

  return (
    <MetadataContext.Provider
      value={{
        bannerMetadata,
        profilePictureMetadata,
        bannerPfpMetadata,
        fetchBannerMetadata,
        fetchProfilePictureMetadata,
        fetchBannerPfpMetadata,
        clearMetadata
      }}
    >
      {children}
    </MetadataContext.Provider>
  );
}

export function useMetadata() {
  const context = useContext(MetadataContext);
  if (context === undefined) {
    throw new Error('useMetadata must be used within a MetadataProvider');
  }
  return context;
}
