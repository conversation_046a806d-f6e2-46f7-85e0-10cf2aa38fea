{"name": "saveme", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup:db": "tsx db/setup.ts", "setup:modified": "tsx db/setup-modified.ts", "drop:image-tables": "tsx db/drop-image-tables.ts", "start:prod": "NODE_ENV=production next start", "test:db": "tsx db/test-connection.ts", "migrate": "tsx db/migrate.ts", "migrate:images": "tsx db/migrate-images.ts", "migrate:comp-position": "tsx db/migrate-comp-position.ts", "migrate:images-to-components": "tsx db/migrate-images-to-components.ts", "migrate:shape": "tsx db/migrate-shape.ts", "migrate:hero": "tsx db/migrate-hero.ts", "migrate:name": "tsx db/migrate-name.ts", "migrate:likes": "tsx db/migrate-likes.ts", "add-system-settings": "tsx db/add-system-settings.ts", "reset-database": "tsx db/reset-database.ts", "check-tables": "tsx db/check-tables.ts", "docker:start": "node scripts/docker-start.js", "docker:stop": "node scripts/docker-stop.js", "docker:reset": "node scripts/docker-reset.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-navigation-menu": "^1.2.9", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@reown/appkit": "^1.7.3", "@reown/appkit-adapter-solana": "^1.7.6", "@reown/appkit-adapter-wagmi": "^1.7.3", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.74.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.42.0", "framer-motion": "^12.9.7", "gl-matrix": "^3.4.3", "lucide-react": "^0.501.0", "motion": "^12.10.0", "mysql2": "^3.14.0", "next": "15.3.0", "next-themes": "^0.4.6", "pino": "^8.16.0", "pino-pretty": "^10.2.3", "postprocessing": "^6.37.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "sonner": "^2.0.3", "swapy": "^1.0.5", "tailwind-merge": "^3.2.0", "three": "^0.175.0", "viem": "^2.27.3", "wagmi": "^2.14.16", "zod": "^3.24.3"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.175.0", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.0", "tailwindcss": "^4", "tsx": "^4.19.3", "typescript": "^5"}}