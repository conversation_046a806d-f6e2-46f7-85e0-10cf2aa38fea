/* TrueFocus component styles */
/* Note: Most styles are already in globals.css, this file is just for reference */

/* These styles are already in globals.css, but we're keeping this file
   to maintain the import in the TrueFocus component */

/* Additional styles to ensure single line display without scrollbars */
.focus-container {
  display: flex !important;
  flex-wrap: nowrap !important;
  white-space: nowrap !important;
  overflow: visible !important; /* Changed from overflow-x: auto to hide scrollbars */
  justify-content: center !important;
  width: 100% !important;
  padding: 0.5rem 1rem !important;
  max-width: 100vw !important;
  background: transparent !important;
}

/* Adjust font size for different screen sizes */
.focus-word {
  font-size: 1.5rem !important; /* Default for very small screens */
  margin: 0 0.3rem !important; /* Add some spacing between words */
}

@media (min-width: 375px) {
  .focus-word {
    font-size: 1.75rem !important; /* Small screens */
  }
}

@media (min-width: 640px) {
  .focus-word {
    font-size: 2.25rem !important; /* Medium screens */
    margin: 0 0.5rem !important;
  }
}

@media (min-width: 768px) {
  .focus-word {
    font-size: 2.75rem !important; /* Large screens */
  }
}
