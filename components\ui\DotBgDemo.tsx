import { cn } from "@/lib/utils";
import React from "react";

interface DotBgProps {
  children?: React.ReactNode;
  className?: string;
  showRadialGradient?: boolean;
  disabled?: boolean;
  dotSize?: number; // Size of dots in pixels
  dotSpacing?: number; // Spacing between dots in pixels
}

export function DotBg({
  children,
  className,
  showRadialGradient = true,
  disabled = false,
  dotSize = 1,
  dotSpacing = 20
}: DotBgProps) {
    // Calculate the background size based on dot spacing
    const bgSize = `${dotSpacing}px ${dotSpacing}px`;

    return (
        <div className={cn(
          "relative w-full",
          className
        )}>
            {!disabled && (
              <>
                {/* Grid pattern with dots at intersections */}
                <div
                    className={cn(
                        "absolute inset-0",
                        "z-0",
                        `[background-size:${bgSize}]`,
                        `[background-image:radial-gradient(#d4d4d4_${dotSize}px,transparent_${dotSize}px)]`,
                        `dark:[background-image:radial-gradient(#404040_${dotSize}px,transparent_${dotSize}px)]`
                    )}
                />
                {/* Radial gradient for the container to give a faded look */}
                {showRadialGradient && (
                  <div className="pointer-events-none absolute inset-0 flex items-center justify-center bg-white [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] dark:bg-black z-0"></div>
                )}
              </>
            )}
            {children}
        </div>
    );
}

// Keep the demo component for reference or testing
export function DotBgDemo() {
    return (
        <DotBg className="flex h-[50rem] items-center justify-center bg-white dark:bg-black">
            <p className="relative z-20 bg-gradient-to-b from-neutral-200 to-neutral-500 bg-clip-text py-8 text-4xl font-bold text-transparent sm:text-7xl">
                Backgrounds
            </p>
        </DotBg>
    );
}
