"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";

export const SmallTypewriterEffect = ({
    words,
    className,
    cursorClassName,
    style,
}: {
    words: {
        text: string;
        className?: string;
    }[];
    className?: string;
    cursorClassName?: string;
    style?: React.CSSProperties;
}) => {
    // split text inside of words into array of characters
    const wordsArray = words.map((word) => {
        return {
            ...word,
            text: word.text.split(""),
        };
    });

    const renderWords = () => {
        return (
            <div className="text-center max-w-full overflow-hidden">
                {wordsArray.map((word, idx) => {
                    return (
                        <div key={`word-${idx}`} className="inline-block max-w-full overflow-hidden">
                            {word.text.map((char, index) => (
                                <span
                                    key={`char-${index}`}
                                    className={cn(``, word.className)}
                                    style={style}
                                >
                                    {char}
                                </span>
                            ))}
                            &nbsp;
                        </div>
                    );
                })}
            </div>
        );
    };

    return (
        <div className={cn("flex space-x-1 max-w-full", className)}>
            <motion.div
                className="overflow-hidden pb-2 max-w-full"
                initial={{
                    width: "0%",
                }}
                whileInView={{
                    width: "fit-content",
                }}
                transition={{
                    duration: 2,
                    ease: "linear",
                    delay: 1,
                }}
            >
                <div
                    className="text-base lg:text-lg font-bold xs:text-sm"
                    style={{
                        whiteSpace: "nowrap",
                        maxWidth: "100%",
                        overflow: "hidden"
                    }}
                >
                    {renderWords()}{" "}
                </div>{" "}
            </motion.div>
            <motion.span
                initial={{
                    opacity: 0,
                }}
                animate={{
                    opacity: 1,
                }}
                transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    repeatType: "reverse",
                }}
                className={cn(
                    "block rounded-sm w-[2px] h-5 lg:h-6 xs:h-4 bg-blue-500 flex-shrink-0",
                    cursorClassName
                )}
            ></motion.span>
        </div>
    );
};
