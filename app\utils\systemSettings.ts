'use server';

import { db } from '@/db/drizzle';
import { systemSettings } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';

interface ComponentDefault {
  componentType: string;
  order: string;
  hidden: string;
  details?: {
    backgroundColor?: string;
    fontColor?: string | null;
    shape?: string;
    profileName?: string;
    profileBio?: string;
    defaultImagePath?: string;
    profileShape?: string;
    profileHorizontalPosition?: number;
    profileNameHorizontalPosition?: number;
    profileNameStyle?: {
      fontSize?: string;
      fontWeight?: string;
      fontColor?: string;
      effect?: string;
    };
    heroContent?: Array<{
      title: string;
      description: string;
      contentType: string;
      colorGradient?: string;
    }>;
    socialLinks?: {
      twitter?: string;
      discord?: string;
      telegram?: string;
      website?: string;
      facebook?: string;
      youtube?: string;
      email?: string;
      linkedin?: string;
      cro?: string;
    };
  };
}

interface ComponentDefaults {
  defaults: ComponentDefault[];
}

/**
 * Get component defaults from system settings
 * @returns Array of default component settings
 */
export async function getComponentDefaults(): Promise<ComponentDefault[]> {
  try {
    // Get component defaults from system settings
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, 'component_defaults'));

    if (settings.length === 0) {
      console.error('No component defaults found in database');
      throw new Error('Component defaults not found in database');
    }

    // Parse component defaults from settings
    const componentDefaults = settings[0].value as ComponentDefaults;
    return componentDefaults.defaults;
  } catch (error) {
    console.error('Error getting component defaults:', error);
    throw error;
  }
}

interface ChainConfig {
  id: string;
  name: string;
}

interface TokenRequirements {
  selectedChain: string;
  chainConfigs: ChainConfig[];
  tokenAddresses: Record<string, string>;
  tokenNames: Record<string, string>;
  minimumHoldings: Record<string, string>;
  burnAmounts: Record<string, string>;
}

/**
 * Get token requirements from system settings
 * @returns Token requirements object
 */
export async function getTokenRequirements(): Promise<TokenRequirements> {
  try {
    // Get token requirements from system settings
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, 'token_requirements'));

    if (settings.length === 0) {
      console.error('No token requirements found in database');
      throw new Error('Token requirements not found in database');
    }

    // Return token requirements from settings
    return settings[0].value as TokenRequirements;
  } catch (error) {
    console.error('Error getting token requirements:', error);
    throw error;
  }
}

/**
 * Get token requirements for a specific chain
 * @param chainId The chain ID to get requirements for
 * @returns Object containing token requirements for the specified chain
 */
export async function getTokenRequirementsForChain(chainId: string): Promise<{
  tokenAddress: string;
  tokenName: string;
  minimumHoldings: string;
  burnAmount: string;
}> {
  try {
    const allRequirements = await getTokenRequirements();

    // Get chain-specific values or use defaults
    const tokenAddress = allRequirements.tokenAddresses[chainId] || '';
    const tokenName = allRequirements.tokenNames?.[chainId] || 'Web3Tools';
    const minimumHoldings = allRequirements.minimumHoldings[chainId] || '0';
    const burnAmount = allRequirements.burnAmounts[chainId] || '0';

    return {
      tokenAddress,
      tokenName,
      minimumHoldings,
      burnAmount
    };
  } catch (error) {
    console.error(`Error getting token requirements for chain ${chainId}:`, error);
    throw error;
  }
}

interface ProfileDefaults {
  default_role: string;
  default_status: string;
  default_expiry_days: string;
  default_profile_name_format: string;
  default_profile_bio: string;
}

/**
 * Initialize default profile settings in the database
 * @returns True if successful, false otherwise
 */
export async function initializeProfileDefaults(): Promise<boolean> {
  try {
    console.log('Initializing default profile settings...');

    // Default profile settings
    const defaultProfileSettings: ProfileDefaults = {
      default_role: 'user',
      default_status: 'new',
      default_expiry_days: '1',
      default_profile_name_format: 'Web3 User {address}',
      default_profile_bio: 'Welcome to my Web3 Social profile!'
    };

    // Insert or update profile defaults in system_settings table
    await db.insert(systemSettings).values({
      id: 'profile_defaults',
      value: defaultProfileSettings,
      createdAt: new Date(),
      updatedAt: new Date()
    }).onDuplicateKeyUpdate({ set: {
      value: sql`${JSON.stringify(defaultProfileSettings)}`,
      updatedAt: new Date()
    }});

    // Default profile settings initialized
    return true;
  } catch (error) {
    console.error('Error initializing default profile settings:', error);
    return false;
  }
}

/**
 * Get profile defaults from system settings
 * @returns Profile defaults object
 */
export async function getProfileDefaults(): Promise<ProfileDefaults> {
  try {
    // Get profile defaults from system settings
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, 'profile_defaults'));

    if (settings.length === 0) {
      console.log('No profile defaults found in database, initializing defaults...');

      // Initialize default profile settings
      const initialized = await initializeProfileDefaults();

      if (!initialized) {
        throw new Error('Failed to initialize profile defaults');
      }

      // Try to get the newly created settings
      const newSettings = await db
        .select()
        .from(systemSettings)
        .where(eq(systemSettings.id, 'profile_defaults'));

      if (newSettings.length === 0) {
        throw new Error('Profile defaults not found in database after initialization');
      }

      return newSettings[0].value as ProfileDefaults;
    }

    // Return profile defaults from settings
    return settings[0].value as ProfileDefaults;
  } catch (error) {
    console.error('Error getting profile defaults:', error);
    throw error;
  }
}

/**
 * Get featured profile name from system settings
 * @returns Featured profile name
 */
export async function getFeaturedProfileName(): Promise<string> {
  try {
    // Get featured profile from system settings
    const settings = await db
      .select()
      .from(systemSettings)
      .where(eq(systemSettings.id, 'featured_profile'));

    if (settings.length === 0) {
      console.log('No featured profile found in database, using default');
      // Return default value if not found in database
      return 'web3tools';
    }

    // Parse featured profile from settings
    return settings[0].value as string;
  } catch (error) {
    console.error('Error getting featured profile:', error);
    // Return default value on error
    return 'web3tools';
  }
}
