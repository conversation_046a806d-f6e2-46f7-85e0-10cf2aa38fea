import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

/**
 * Generate a unique referral code in the format w3txxxxx
 * @returns Promise<string> - The generated referral code
 */
export async function generateReferralCode(): Promise<string> {
  const maxAttempts = 10;
  let attempts = 0;

  while (attempts < maxAttempts) {
    // Generate 5 random alphanumeric characters (lowercase for consistency)
    const randomChars = Math.random().toString(36).substring(2, 7);
    const referralCode = `w3t${randomChars}`;

    // Check if this code already exists
    const existingCode = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.referralCode, referralCode));

    if (existingCode.length === 0) {
      return referralCode;
    }

    attempts++;
  }

  throw new Error('Failed to generate unique referral code after maximum attempts');
}

/**
 * Validate if a referral code exists and belongs to an approved user
 * @param referralCode - The referral code to validate
 * @returns Promise<{ isValid: boolean, referrerAddress?: string }> - Validation result
 */
export async function validateReferralCode(referralCode: string): Promise<{ isValid: boolean, referrerAddress?: string }> {
  // Normalize the referral code to lowercase for validation
  const normalizedCode = referralCode.toLowerCase();

  if (!referralCode || referralCode.length !== 8 || !normalizedCode.startsWith('w3t')) {
    return { isValid: false };
  }

  try {
    const referrer = await db
      .select({
        address: web3Profile.address,
        status: web3Profile.status
      })
      .from(web3Profile)
      .where(eq(web3Profile.referralCode, normalizedCode));

    if (referrer.length === 0) {
      return { isValid: false };
    }

    const referrerData = referrer[0];

    // Only approved users can refer others
    if (referrerData.status !== 'approved') {
      return { isValid: false };
    }

    return {
      isValid: true,
      referrerAddress: referrerData.address
    };
  } catch (error) {
    console.error('Error validating referral code:', error);
    return { isValid: false };
  }
}

/**
 * Get referral statistics for a user
 * @param address - The user's address
 * @returns Promise<{ referralCode?: string, referralCount: number, referredBy?: string }> - Referral stats
 */
export async function getReferralStats(address: string): Promise<{
  referralCode?: string,
  referralCount: number,
  referredBy?: string
}> {
  try {
    // Get user's referral code and who referred them
    const userProfile = await db
      .select({
        referralCode: web3Profile.referralCode,
        referredBy: web3Profile.referredBy
      })
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (userProfile.length === 0) {
      return { referralCount: 0 };
    }

    const profile = userProfile[0];

    // Count how many approved people this user has referred
    const referralCount = await db
      .select()
      .from(web3Profile)
      .where(
        and(
          eq(web3Profile.referredBy, profile.referralCode || ''),
          eq(web3Profile.status, 'approved')
        )
      );

    return {
      referralCode: profile.referralCode || undefined,
      referralCount: referralCount.length,
      referredBy: profile.referredBy || undefined
    };
  } catch (error) {
    console.error('Error getting referral stats:', error);
    return { referralCount: 0 };
  }
}
