'use client';

import React from 'react';
import { GridSmallBg } from "@/components/ui/GridSmallBg";
import { useGridBg } from "@/app/contexts/GridBgContext";

// Client component to conditionally render the grid background
export default function GridBgWrapper({ 
  children, 
  className 
}: { 
  children: React.ReactNode, 
  className?: string 
}) {
  const { isGridBgDisabled } = useGridBg();
  
  return (
    <GridSmallBg className={className} disabled={isGridBgDisabled}>
      {children}
    </GridSmallBg>
  );
}
