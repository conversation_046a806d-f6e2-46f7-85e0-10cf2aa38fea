# Local Development Guide

This guide will help you set up the Web3Profiles application for local development.

## Prerequisites

- Node.js (v18 or later)
- MySQL (v8.0 or later)

## 1. Install MySQL

### Windows

1. Download and install MySQL from [the official website](https://dev.mysql.com/downloads/installer/)
2. During installation, set a password for the root user
3. Keep the default port (3306)
4. Complete the installation

### macOS

Using Homebrew:

```bash
brew install mysql
brew services start mysql
```

### Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
sudo mysql_secure_installation
```

## 2. Create a Database

### Windows

1. Open MySQL Workbench (installed with MySQL)
2. Connect to the server using the password you set during installation
3. Click on the SQL editor and run the following commands:
   ```sql
   CREATE DATABASE web3profiles CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'web3user'@'localhost' IDENTIFIED BY 'your_password';
   GRANT ALL PRIVILEGES ON web3profiles.* TO 'web3user'@'localhost';
   FLUSH PRIVILEGES;
   ```

### macOS/Linux

```bash
# Log in to MySQL as root
sudo mysql -u root -p
# Enter your root password when prompted

# Create a database and user
mysql> CREATE DATABASE web3profiles CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
mysql> CREATE USER 'web3user'@'localhost' IDENTIFIED BY 'your_password';
mysql> GRANT ALL PRIVILEGES ON web3profiles.* TO 'web3user'@'localhost';
mysql> FLUSH PRIVILEGES;
mysql> EXIT;
```

## 3. Clone and Set Up the Application

```bash
# Clone the repository
git clone https://your-repository-url.git web3profiles
cd web3profiles

# Install dependencies
npm install

# Create .env file
cp .env.example .env
```

Edit the `.env` file with your local settings:

```
DATABASE_URL=mysql://web3user:your_password@localhost:3306/web3profiles
MYSQL_SSL=false
NEXT_PUBLIC_PROJECT_ID=your_reown_project_id
NODE_ENV=development
```

## 4. Set Up the Database

```bash
# Run database setup script
npm run setup:db

# Run migrations
npm run migrate
npm run migrate:images
```

## 5. Start the Development Server

```bash
npm run dev
```

The application will be available at http://localhost:3000

## 6. Development Workflow

### Database Changes

If you make changes to the database schema:

1. Update the schema in `db/schema.ts`
2. Create a new migration file if needed
3. Run the migration with `npm run migrate`

### Testing Database Connections

You can test your database connection by running:

```bash
# Create a test script
echo "import { db } from './db/drizzle'; async function test() { try { const result = await db.execute(sql\`SELECT 1\`); console.log('Database connection successful:', result); } catch (error) { console.error('Database connection failed:', error); } } test();" > test-db.ts

# Run the test
npx tsx test-db.ts
```

### Common Issues

#### Connection Refused

If you see "connection refused" errors:

- Check that MySQL is running
- Verify the connection string in your `.env` file
- Make sure the username and password are correct
- Check that the database exists

#### SSL Issues

If you encounter SSL-related errors:

- Set `MYSQL_SSL=false` in your `.env` file for local development

#### Migration Errors

If migrations fail:

- Check the database logs
- Make sure your database user has the necessary permissions
- Try running the migrations manually using the SQL in the migration files
