# Using Docker with Web3Profiles

This guide explains how to use Docker to run MySQL for your Web3Profiles application.

## Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop/) installed on your system
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker Desktop)

## Quick Start

1. **Start MySQL in Docker**:

   ```bash
   npm run docker:start
   ```

   This will:
   - Start a MySQL 8.0 container
   - Create a database named `web3profiles`
   - Create a user `web3user` with password `web3password`
   - Expose MySQL on port 3306
   - Create a `.env` file from `.env.docker` if it doesn't exist

2. **Set up the database**:

   ```bash
   npm run setup:db
   npm run migrate
   npm run migrate:images
   ```

3. **Start the development server**:

   ```bash
   npm run dev
   ```

4. **Stop the Docker container when done**:

   ```bash
   npm run docker:stop
   ```

## Docker Commands

| Command | Description |
|---------|-------------|
| `npm run docker:start` | Start MySQL container |
| `npm run docker:stop` | Stop MySQL container |
| `npm run docker:reset` | Reset MySQL container (deletes all data) |

## Connection Details

- **Host**: localhost
- **Port**: 3306
- **Database**: web3profiles
- **Username**: web3user
- **Password**: web3password
- **Connection URL**: `mysql://web3user:web3password@localhost:3306/web3profiles`

## Accessing MySQL CLI

You can access the MySQL command line interface with:

```bash
docker exec -it web3profiles-mysql mysql -u web3user -pweb3password web3profiles
```

## Viewing Logs

To view MySQL logs:

```bash
docker logs web3profiles-mysql
```

## Data Persistence

Data is stored in a Docker volume named `mysql-data`. This means:

- Data persists between container restarts
- To completely reset the database, use `npm run docker:reset`

## Troubleshooting

### Container won't start

Check if port 3306 is already in use:

```bash
netstat -ano | findstr :3306  # Windows
lsof -i :3306                 # Mac/Linux
```

### Connection issues

Make sure the container is running:

```bash
docker ps
```

Check container logs for errors:

```bash
docker logs web3profiles-mysql
```
