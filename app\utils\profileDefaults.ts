import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import { getComponentDefaults, getProfileDefaults } from './systemSettings';
import { getComponentImages } from '@/lib/imageStorage';

/**
 * Check if a profile exists for the given address
 * @param address Wallet address
 * @returns Boolean indicating if profile exists
 */
export async function profileExists(address: string): Promise<boolean> {
  if (!address) return false;

  try {
    const existingProfile = await db
      .select({ count: sql`count(*)` })
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    return existingProfile.length > 0 && Number(existingProfile[0]?.count) > 0;
  } catch (error) {
    console.error('Error checking if profile exists:', error);
    return false;
  }
}

/**
 * Check if component positions exist for the given address
 * @param address Wallet address
 * @returns Boolean indicating if component positions exist
 */
export async function componentPositionsExist(address: string): Promise<boolean> {
  if (!address) return false;

  try {
    const existingPositions = await db
      .select({ count: sql`count(*)` })
      .from(componentPositions)
      .where(eq(componentPositions.address, address));

    return existingPositions.length > 0 && Number(existingPositions[0]?.count) > 0;
  } catch (error) {
    console.error('Error checking if component positions exist:', error);
    return false;
  }
}

/**
 * Create default profile and component positions for a new user
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 */
export async function createDefaultProfile(address: string, chain: string): Promise<void> {
  if (!address) return;

  try {
    console.log(`Creating default profile for address: ${address}`);

    // Check if profile already exists
    const profileExists = await db
      .select({ count: sql`count(*)` })
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    // Only create profile if it doesn't exist
    if (profileExists.length === 0 || Number(profileExists[0]?.count) === 0) {
      // Get profile defaults from system settings
      const profileDefaults = await getProfileDefaults();

      // Calculate expiry date if default_expiry_days is set
      let expiryDate = null;
      if (profileDefaults.default_expiry_days) {
        const days = parseInt(profileDefaults.default_expiry_days);
        if (!isNaN(days) && days > 0) {
          expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + days);
        }
      }

      // Format the default profile name using the template from settings
      const defaultNameFormat = profileDefaults.default_profile_name_format;
      const defaultName = defaultNameFormat.replace('{address}', address.substring(0, 8));

      // Create a URL-friendly name (single word) by removing spaces and special characters
      const urlName = defaultName.replace(/\s+/g, '').replace(/[^a-zA-Z0-9]/g, '');

      // Get default bio from settings
      const defaultBio = profileDefaults.default_profile_bio;

      // Create profile with values from database defaults
      // Note: name and bio are now stored in the componentPositions table for the profilePicture component
      await db.insert(web3Profile).values({
        address,
        chain,
        name: urlName, // Set the name field to be used in URLs (single word)
        role: profileDefaults.default_role,
        status: profileDefaults.default_status,
        expiryDate,
        transactionHash: null,
      });
      console.log(`Created profile for address: ${address}`);

      // Create default profile picture image
      try {
        const { readFileSync } = require('fs');
        const { join } = require('path');
        const { saveImage } = await import('@/lib/imageStorage');

        // Read default profile image
        const filepath = join(process.cwd(), 'public', 'pfp.jpg');
        const buffer = readFileSync(filepath);
        const defaultImageBase64 = buffer.toString('base64');

        // Save default image to componentImages table
        await saveImage(address, 'profilePicture', '0', defaultImageBase64);
        console.log(`Created default profile picture image for address: ${address}`);
      } catch (error) {
        console.error('Error creating default profile picture image:', error);
        // Continue even if image creation fails
      }
    } else {
      console.log(`Profile already exists for address: ${address}`);
    }

    // Check if component positions already exist
    const positionsExist = await componentPositionsExist(address);

    // Only create component positions if they don't exist
    if (!positionsExist) {
      console.log(`Creating default component positions for address: ${address}`);

      // Get component defaults from system settings
      const componentDefaults = await getComponentDefaults();

      // Get profile defaults from system settings
      const profileDefaults = await getProfileDefaults();

      // Insert components based on database defaults
      for (const defaultComponent of componentDefaults) {
        // Format the default profile name using the template from settings
        const defaultNameFormat = profileDefaults.default_profile_name_format;
        const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));

        // Get default bio from settings
        const defaultBio = profileDefaults.default_profile_bio;

        // Prepare component data with details field
        const componentData: any = {
          address,
          chain,
          componentType: defaultComponent.componentType,
          order: defaultComponent.order,
          hidden: defaultComponent.hidden,
          details: {
            backgroundColor: 'transparent',
            fontColor: null
          }
        };

        // Add component-specific details based on component type
        if (defaultComponent.componentType === 'banner') {
          // Banner doesn't need any additional details beyond the common ones
        } else if (defaultComponent.componentType === 'profilePicture') {
          componentData.details = {
            ...componentData.details,
            shape: 'circular',
            profileName: formattedName,
            profileBio: defaultBio
          };
        } else if (defaultComponent.componentType === 'hero') {
          componentData.details = {
            ...componentData.details,
            heroContent: [
              {
                title: "My First Section",
                description: "This is my first section. Click edit to change this text.",
                contentType: 'color',
                colorGradient: 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
              }
            ]
          };
        } else if (defaultComponent.componentType === 'socialLinks') {
          // Use socialLinks from system settings if available, otherwise use empty defaults
          const defaultSocialLinks = defaultComponent.details?.socialLinks || {
            twitter: '',
            discord: '',
            telegram: '',
            website: '',
            facebook: '',
            youtube: '',
            email: '',
            linkedin: '',
            cro: ''
          };

          componentData.details = {
            ...componentData.details,
            backgroundColor: defaultComponent.details?.backgroundColor || 'transparent',
            fontColor: defaultComponent.details?.fontColor || null,
            socialLinks: defaultSocialLinks
          };
        }

        await db.insert(componentPositions).values(componentData);

        // Create default image positioning in componentImages table for image components
        if (defaultComponent.componentType === 'banner' || defaultComponent.componentType === 'profilePicture') {
          const { saveImagePositioning } = await import('@/lib/imageStorage');
          await saveImagePositioning(address, defaultComponent.componentType, '0', {
            scale: 1,
            positionX: 0,
            positionY: 0,
            naturalWidth: null,
            naturalHeight: null
          });
        }
      }

      console.log(`Created default component positions for address: ${address}`);
    } else {
      console.log(`Component positions already exist for address: ${address}`);
    }
  } catch (error) {
    console.error('Error creating default profile:', error);
    throw error;
  }
}

/**
 * Get profile data with components for the given address
 * If profile doesn't exist, create default profile and components
 * @param address Wallet address
 * @param chain Blockchain network chain ID
 * @returns Profile data with components
 */
export async function getOrCreateProfile(address: string, chain: string): Promise<any> {
  if (!address) throw new Error('Address is required');

  try {
    console.log(`Getting or creating profile for address: ${address}`);

    // Check if profile exists
    const profileExists = await db
      .select({ count: sql`count(*)` })
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    // If profile doesn't exist, create default profile
    if (profileExists.length === 0 || Number(profileExists[0]?.count) === 0) {
      console.log(`Profile doesn't exist for address: ${address}, creating default`);
      await createDefaultProfile(address, chain);
    } else {
      console.log(`Profile exists for address: ${address}`);

      // Check if component positions exist
      const positionsExist = await componentPositionsExist(address);

      // If component positions don't exist, create default component positions
      if (!positionsExist) {
        console.log(`Component positions don't exist for address: ${address}, creating default`);

        // Get component defaults from system settings
        const componentDefaults = await getComponentDefaults();

        // Insert components based on database defaults
        for (const defaultComponent of componentDefaults) {
          // Format the default profile name using the template from settings
          const profileDefaults = await getProfileDefaults();
          const defaultNameFormat = profileDefaults.default_profile_name_format;
          const formattedName = defaultNameFormat.replace('{address}', address.substring(0, 8));

          // Get default bio from settings
          const defaultBio = profileDefaults.default_profile_bio;

          // Prepare component data with details field
          const componentData: any = {
            address,
            chain,
            componentType: defaultComponent.componentType,
            order: defaultComponent.order,
            hidden: defaultComponent.hidden,
            details: {
              backgroundColor: 'transparent',
              fontColor: null
            }
          };

          // Add component-specific details based on component type
          if (defaultComponent.componentType === 'banner') {
            // Banner doesn't need any additional details beyond the common ones
          } else if (defaultComponent.componentType === 'profilePicture') {
            componentData.details = {
              ...componentData.details,
              shape: 'circular',
              profileName: formattedName,
              profileBio: defaultBio
            };
          } else if (defaultComponent.componentType === 'hero') {
            componentData.details = {
              ...componentData.details,
              heroContent: [
                {
                  title: "My First Section",
                  description: "This is my first section. Click edit to change this text.",
                  contentType: 'color',
                  colorGradient: 'linear-gradient(to bottom right, var(--cyan-500), var(--emerald-500))'
                }
              ]
            };
          } else if (defaultComponent.componentType === 'socialLinks') {
            // Use socialLinks from system settings if available, otherwise use empty defaults
            const defaultSocialLinks = defaultComponent.details?.socialLinks || {
              twitter: '',
              discord: '',
              telegram: '',
              website: '',
              facebook: '',
              youtube: '',
              email: '',
              linkedin: '',
              cro: ''
            };

            componentData.details = {
              ...componentData.details,
              backgroundColor: defaultComponent.details?.backgroundColor || 'transparent',
              fontColor: defaultComponent.details?.fontColor || null,
              socialLinks: defaultSocialLinks
            };
          }

          await db.insert(componentPositions).values(componentData);

          // Create default image positioning in componentImages table for image components
          if (defaultComponent.componentType === 'banner' || defaultComponent.componentType === 'profilePicture') {
            const { saveImagePositioning } = await import('@/lib/imageStorage');
            await saveImagePositioning(address, defaultComponent.componentType, '0', {
              scale: 1,
              positionX: 0,
              positionY: 0,
              naturalWidth: null,
              naturalHeight: null
            });
          }
        }

        console.log(`Created default component positions for address: ${address}`);
      } else {
        console.log(`Component positions exist for address: ${address}`);
      }
    }

    // Get profile data
    const profile = await db
      .select()
      .from(web3Profile)
      .where(eq(web3Profile.address, address));

    if (profile.length === 0) {
      throw new Error('Failed to create or retrieve profile');
    }

    // Get component positions
    const components = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.address, address))
      .orderBy(componentPositions.order);

    console.log(`Found ${components.length} components for address: ${address}`);

    // Get component images for positioning data
    const componentImagesMap = new Map();
    for (const component of components) {
      if (component.componentType === 'banner' || component.componentType === 'profilePicture') {
        const images = await getComponentImages(address, component.componentType);
        if (images.length > 0) {
          componentImagesMap.set(component.componentType, images[0]);
        }
      }
    }

    // Return profile data with components
    return {
      ...profile[0],
      components: components.map(c => {
        const details: {
          backgroundColor?: string;
          fontColor?: string | null;
          socialLinks?: any;
          profileName?: string;
          profileBio?: string;
          [key: string]: any;
        } = c.details as any;
        const image = componentImagesMap.get(c.componentType);

        return {
          componentType: c.componentType,
          order: c.order,
          hidden: c.hidden,
          backgroundColor: details.backgroundColor,
          fontColor: details.fontColor,
          position: image ? { x: image.positionX, y: image.positionY } : undefined,
          details: c.details // Include the full details object
        };
      }),
    };
  } catch (error) {
    console.error('Error getting or creating profile:', error);
    throw error;
  }
}
