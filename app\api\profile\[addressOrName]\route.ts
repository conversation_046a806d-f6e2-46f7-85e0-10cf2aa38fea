import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile, componentPositions } from '@/db/schema';
import { eq } from 'drizzle-orm';

type Context = {
  params: Promise<{
    addressOrName: string;
  }>;
};

export async function GET(
  _request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { addressOrName } = await context.params;

    // Check if addressOrName is undefined, null, or empty
    if (!addressOrName || addressOrName === 'undefined') {
      console.error('Invalid addressOrName parameter:', addressOrName);
      return Response.json(
        { error: 'Valid address or name is required' },
        { status: 400 }
      );
    }

    // Getting profile for the provided address or name
    const allProfiles = await db.select().from(web3Profile);

    // Check if there are any profiles at all
    if (allProfiles.length === 0) {
      // No profiles exist yet
      return Response.json(
        { error: 'No profiles exist in the database yet' },
        { status: 404 }
      );
    }

    // Also log all component positions with profileName
    const allBannerpfpComponents = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.componentType, 'bannerpfp'));

    console.log('All bannerpfp components:', allBannerpfpComponents.map(c => {
      const details = c.details as any;
      return {
        address: c.address,
        profileName: details?.profileName,
      };
    }));

    // Check if there are any bannerpfp components
    if (allBannerpfpComponents.length === 0) {
      console.log('No bannerpfp components found in the database');
      // Don't return an error, just log it
      console.log('Profile components not properly initialized');
    }

    // Check if the input is an Ethereum address (0x...)
    // Note: Even if it starts with 0x, it could still be a name derived from an address
    const isAddress = addressOrName.startsWith('0x') && addressOrName.length >= 40;

    let profile: any;
    let matchingComponents: any[] = [];
    let searchTerm = '';
    let bannerpfpComponents: any[] = [];

    if (isAddress) {
      // If it's an address, ensure defaults and get profile data
      console.log(`Treating ${addressOrName} as an address`);

      // Get profile data
      profile = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.address, addressOrName));

      if (profile.length === 0) {
        // If not found by address, try looking up by name as a fallback
        console.log(`Profile not found by address, trying as name: ${addressOrName}`);

        // First try to find a profile with this name in the web3Profile table
        const profilesByName = await db
          .select()
          .from(web3Profile)
          .where(eq(web3Profile.name, addressOrName));

        if (profilesByName.length > 0) {
          console.log(`Found profile by name in web3Profile table: ${addressOrName}`);
          profile = profilesByName;
        } else {
          // If not found in web3Profile table, try looking up by name in bannerpfp component
          console.log(`Profile not found by name in web3Profile table, trying in bannerpfp components: ${addressOrName}`);

          // Find a bannerpfp component with this name in the details field
          bannerpfpComponents = await db
            .select()
            .from(componentPositions)
            .where(eq(componentPositions.componentType, 'bannerpfp'));

          // Filter components by profileName in the details field
          // Use case-insensitive comparison and trim whitespace
          searchTerm = addressOrName.trim().toLowerCase();
          console.log(`Searching for profile with name (normalized): "${searchTerm}"`);

          // First try exact match
          matchingComponents = bannerpfpComponents.filter(comp => {
            const details = comp.details || {};
            const storedName = details.profileName?.trim().toLowerCase();
            const storedUrlName = details.urlName?.trim().toLowerCase();
            console.log(`Comparing with: profileName="${storedName}", urlName="${storedUrlName}" from address ${comp.address}`);
            return storedName === searchTerm || storedUrlName === searchTerm;
          });
        }
      }
    } else {
      // If it's a name, look up the profile by name
      console.log(`Treating ${addressOrName} as a name`);

      // First try to find a profile with this name in the web3Profile table
      const profilesByName = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.name, addressOrName));

      if (profilesByName.length > 0) {
        console.log(`Found profile by name in web3Profile table: ${addressOrName}`);
        profile = profilesByName;
      } else {
        // If not found in web3Profile table, try looking up by name in bannerpfp component
        console.log(`Profile not found by name in web3Profile table, trying in bannerpfp components: ${addressOrName}`);

        // Find a bannerpfp component with this name in the details field
        bannerpfpComponents = await db
          .select()
          .from(componentPositions)
          .where(eq(componentPositions.componentType, 'bannerpfp'));

        // Filter components by profileName in the details field
        // Use case-insensitive comparison and trim whitespace
        searchTerm = addressOrName.trim().toLowerCase();
        console.log(`Searching for profile with name (normalized): "${searchTerm}"`);

        // First try exact match
        matchingComponents = bannerpfpComponents.filter(comp => {
          const details = comp.details || {};
          const storedName = details.profileName?.trim().toLowerCase();
          const storedUrlName = details.urlName?.trim().toLowerCase();
          console.log(`Comparing with: profileName="${storedName}", urlName="${storedUrlName}" from address ${comp.address}`);
          return storedName === searchTerm || storedUrlName === searchTerm;
        });
      }
    }

    // If no exact matches, try partial match
    if ((!profile || profile.length === 0) &&
        (!matchingComponents || matchingComponents.length === 0) &&
        searchTerm.length > 3) {
      console.log('No exact matches found, trying partial matches...');
      matchingComponents = bannerpfpComponents.filter(comp => {
        const details = comp.details || {};
        const storedName = details.profileName?.trim().toLowerCase();
        const storedUrlName = details.urlName?.trim().toLowerCase();
        return storedName?.includes(searchTerm) || searchTerm.includes(storedName || '') ||
               storedUrlName?.includes(searchTerm) || searchTerm.includes(storedUrlName || '');
      });

      if (matchingComponents.length > 0) {
        console.log(`Found ${matchingComponents.length} partial matches`);
      }
    }

    // If we found matching components but no profile, get the profile by address
    if ((!profile || profile.length === 0) && matchingComponents.length > 0) {
      const matchingAddress = matchingComponents[0].address;
      console.log(`Found matching component for address: ${matchingAddress}`);

      profile = await db
        .select()
        .from(web3Profile)
        .where(eq(web3Profile.address, matchingAddress));

      if (profile.length === 0) {
        console.log(`No profile found for address: ${matchingAddress}`);
        return Response.json(
          { error: 'Profile not found' },
          { status: 404 }
        );
      }

      // Found by name, continue with this profile
      console.log(`Found profile by name: ${matchingAddress}`);
    }

    // If no profile found, return error
    if (!profile || profile.length === 0) {
      console.log('No profile found');
      return Response.json(
        { error: 'Profile not found' },
        { status: 404 }
      );
    }

    // Get component positions
    const components = await db
      .select()
      .from(componentPositions)
      .where(eq(componentPositions.address, profile[0].address))
      .orderBy(componentPositions.order);

    // Return profile data with components
    return Response.json({
      ...profile[0],
      components: components.map(c => {
        const details = (c.details || {}) as any;
        return {
          ...c,
          backgroundColor: details.backgroundColor,
          fontColor: details.fontColor,
          socialLinks: c.componentType === 'socialLinks' ? details.socialLinks : undefined,
          heroContent: c.componentType === 'hero' ? details.heroContent : undefined,
          profileName: c.componentType === 'bannerpfp' ? details.profileName : undefined,
          profileBio: c.componentType === 'bannerpfp' ? details.profileBio : undefined,
          urlName: c.componentType === 'bannerpfp' ? details.urlName : undefined,
          profileShape: c.componentType === 'bannerpfp' ? details.profileShape : undefined,
          profileNameStyle: c.componentType === 'bannerpfp' ? details.profileNameStyle : undefined,
          profileHorizontalPosition: c.componentType === 'bannerpfp' ? details.profileHorizontalPosition : undefined,
          profileNameHorizontalPosition: c.componentType === 'bannerpfp' ? details.profileNameHorizontalPosition : undefined,
          details: c.details // Include the full details object
        };
      }),
    });
  } catch (error: any) {
    console.error('Failed to fetch profile:', error);

    // Check for database connection error
    if (error.code === 'ECONNREFUSED') {
      return Response.json(
        { error: 'Database is currently unavailable. Please try again later.' },
        { status: 503 }
      );
    }

    return Response.json(
      { error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}
