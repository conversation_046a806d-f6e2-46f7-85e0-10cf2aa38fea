'use client';

import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import Link from 'next/link';
import { checkProfileStatus } from '@/lib/profileStatus';

export default function TestStatusPage() {
  const { address, isConnected } = useAccount();
  const [profileName, setProfileName] = useState('');
  const [statusResult, setStatusResult] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [allProfiles, setAllProfiles] = useState<any[]>([]);
  const [isLoadingProfiles, setIsLoadingProfiles] = useState(false);

  // Load all profiles on page load
  useEffect(() => {
    async function loadProfiles() {
      try {
        setIsLoadingProfiles(true);
        const response = await fetch('/api/profile');
        if (response.ok) {
          const data = await response.json();
          setAllProfiles(data);
        }
      } catch (error) {
        console.error('Error loading profiles:', error);
      } finally {
        setIsLoadingProfiles(false);
      }
    }

    loadProfiles();
  }, []);

  const checkStatus = async () => {
    if (!profileName) return;
    
    setIsChecking(true);
    try {
      const result = await checkProfileStatus(profileName);
      setStatusResult(result);
    } catch (error) {
      console.error('Error checking status:', error);
      setStatusResult({ error: 'Failed to check status' });
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <div className="container max-w-4xl mx-auto py-12 px-4">
      <h1 className="text-2xl font-bold mb-6">Profile Status Test</h1>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
        <div className="mb-4">
          <label htmlFor="profileName" className="block text-sm font-medium mb-2">
            Profile Name or Address
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              id="profileName"
              value={profileName}
              onChange={(e) => setProfileName(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100"
              placeholder="Enter profile name or address"
            />
            <button
              onClick={checkStatus}
              disabled={isChecking || !profileName}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isChecking ? 'Checking...' : 'Check Status'}
            </button>
          </div>
        </div>

        {statusResult && (
          <div className="mt-6 p-4 border rounded-md">
            <h2 className="text-lg font-semibold mb-2">Status Result</h2>
            <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-auto">
              {JSON.stringify(statusResult, null, 2)}
            </pre>
            
            {statusResult.status && (
              <div className="mt-4">
                <p className="mb-2">
                  <strong>Status:</strong>{' '}
                  <span className={`px-2 py-1 rounded ${
                    statusResult.isApproved ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 
                    'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                  }`}>
                    {statusResult.status}
                  </span>
                </p>
                <p className="mb-2"><strong>Approved:</strong> {statusResult.isApproved ? 'Yes' : 'No'}</p>
                <p><strong>Message:</strong> {statusResult.message}</p>
                
                {statusResult.status !== 'not-found' && (
                  <div className="mt-4 flex gap-2">
                    <Link 
                      href={`/${profileName}`}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-block"
                    >
                      Visit Profile
                    </Link>
                    <Link 
                      href={`/profile-error?status=${statusResult.status}`}
                      className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 inline-block"
                    >
                      View Error Page
                    </Link>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <h2 className="text-xl font-semibold mb-4">All Profiles</h2>
        
        {isLoadingProfiles ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : allProfiles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {allProfiles.map((profile) => (
              <div key={profile.address} className="border rounded-md p-4">
                <h3 className="font-medium">{profile.name || profile.address.substring(0, 10)}</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                  {profile.address.substring(0, 10)}...
                </p>
                <p className="text-sm mb-2">
                  <strong>Status:</strong>{' '}
                  <span className={`px-2 py-0.5 rounded text-xs ${
                    profile.status === 'approved' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 
                    'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                  }`}>
                    {profile.status || 'unknown'}
                  </span>
                </p>
                <div className="flex gap-2 mt-2">
                  <button
                    onClick={() => {
                      setProfileName(profile.name || profile.address);
                      checkProfileStatus(profile.name || profile.address).then(setStatusResult);
                    }}
                    className="px-2 py-1 bg-blue-600 text-white text-xs rounded"
                  >
                    Check Status
                  </button>
                  <Link 
                    href={`/${profile.name || profile.address}`}
                    className="px-2 py-1 bg-gray-600 text-white text-xs rounded inline-block"
                  >
                    Visit Profile
                  </Link>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center py-8 text-gray-500">No profiles found</p>
        )}
      </div>
    </div>
  );
}
