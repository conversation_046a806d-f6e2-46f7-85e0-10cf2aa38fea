warning: in the working copy of 'app/admin/page.tsx', <PERSON><PERSON> will be replaced by <PERSON><PERSON> the next time Git touches it
[1mdiff --git a/app/admin/page.tsx b/app/admin/page.tsx[m
[1mindex 335d325..46b7987 100644[m
[1m--- a/app/admin/page.tsx[m
[1m+++ b/app/admin/page.tsx[m
[36m@@ -32,7 +32,8 @@[m [mimport {[m
   Shield,[m
   User,[m
   ExternalLink,[m
[31m-  Plus[m
[32m+[m[32m  Plus,[m
[32m+[m[32m  Loader2[m
 } from 'lucide-react';[m
 import { format } from 'date-fns';[m
 import {[m
[36m@@ -144,6 +145,7 @@[m [minterface User {[m
 export default function AdminPage() {[m
   const [users, setUsers] = useState<User[]>([]);[m
   const [settings, setSettings] = useState<SystemSetting[]>([]);[m
[32m+[m[32m  const [featuredProfile, setFeaturedProfile] = useState<string>('web3tools');[m
   const [tokenRequirements, setTokenRequirements] = useState<TokenRequirements>({[m
     selectedChain: '25',[m
     chainConfigs: [],[m
[36m@@ -195,6 +197,12 @@[m [mexport default function AdminPage() {[m
       if (componentDefaultsSetting) {[m
         setComponentDefaults(componentDefaultsSetting.value);[m
       }[m
[32m+[m
[32m+[m[32m      // Parse featured profile[m
[32m+[m[32m      const featuredProfileSetting = data.find((setting: SystemSetting) => setting.id === 'featured_profile');[m
[32m+[m[32m      if (featuredProfileSetting) {[m
[32m+[m[32m        setFeaturedProfile(featuredProfileSetting.value);[m
[32m+[m[32m      }[m
     } catch (error) {[m
       console.error('Error fetching settings:', error);[m
       toast.error('Failed to fetch system settings');[m
[36m@@ -401,6 +409,32 @@[m [mexport default function AdminPage() {[m
     }[m
   };[m
 [m
[32m+[m[32m  // Update featured profile[m
[32m+[m[32m  const updateFeaturedProfile = async () => {[m
[32m+[m[32m    try {[m
[32m+[m[32m      const response = await fetch('/api/admin/settings', {[m
[32m+[m[32m        method: 'POST',[m
[32m+[m[32m        headers: {[m
[32m+[m[32m          'Content-Type': 'application/json',[m
[32m+[m[32m        },[m
[32m+[m[32m        body: JSON.stringify({[m
[32m+[m[32m          id: 'featured_profile',[m
[32m+[m[32m          value: featuredProfile,[m
[32m+[m[32m          address: address[m
[32m+[m[32m        }),[m
[32m+[m[32m      });[m
[32m+[m
[32m+[m[32m      if (!response.ok) {[m
[32m+[m[32m        throw new Error('Failed to update featured profile');[m
[32m+[m[32m      }[m
[32m+[m
[32m+[m[32m      toast.success('Featured profile updated successfully');[m
[32m+[m[32m    } catch (error) {[m
[32m+[m[32m      console.error('Error updating featured profile:', error);[m
[32m+[m[32m      toast.error('Failed to update featured profile');[m
[32m+[m[32m    }[m
[32m+[m[32m  };[m
[32m+[m
   // Update expiry date[m
   const updateExpiryDate = async (userAddress: string, expiryDate: string | null) => {[m
     try {[m
[36m@@ -977,6 +1011,43 @@[m [mexport default function AdminPage() {[m
             </Card>[m
           </div>[m
 [m
[32m+[m[32m          {/* Featured Profile Card */}[m
[32m+[m[32m          <Card className="mb-6">[m
[32m+[m[32m            <CardHeader>[m
[32m+[m[32m              <CardTitle>Featured Profile</CardTitle>[m
[32m+[m[32m              <CardDescription>Set the profile to display on the home page</CardDescription>[m
[32m+[m[32m            </CardHeader>[m
[32m+[m[32m            <CardContent>[m
[32m+[m[32m              <div className="space-y-4">[m
[32m+[m[32m                <div className="space-y-2">[m
[32m+[m[32m                  <Label htmlFor="featured-profile">Featured Profile Name</Label>[m
[32m+[m[32m                  <Input[m
[32m+[m[32m                    id="featured-profile"[m
[32m+[m[32m                    value={featuredProfile}[m
[32m+[m[32m                    onChange={(e) => setFeaturedProfile(e.target.value)}[m
[32m+[m[32m                    placeholder="e.g., web3tools"[m
[32m+[m[32m                  />[m
[32m+[m[32m                  <p className="text-sm text-muted-foreground">[m
[32m+[m[32m                    Enter the name of the profile you want to feature on the home page.[m
[32m+[m[32m                    Make sure this profile exists in the database.[m
[32m+[m[32m                  </p>[m
[32m+[m[32m                </div>[m
[32m+[m[32m              </div>[m
[32m+[m[32m            </CardContent>[m
[32m+[m[32m            <CardFooter>[m
[32m+[m[32m              <Button onClick={updateFeaturedProfile} disabled={settingsLoading}>[m
[32m+[m[32m                {settingsLoading ? ([m
[32m+[m[32m                  <>[m
[32m+[m[32m                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />[m
[32m+[m[32m                    Saving...[m
[32m+[m[32m                  </>[m
[32m+[m[32m                ) : ([m
[32m+[m[32m                  'Save Featured Profile'[m
[32m+[m[32m                )}[m
[32m+[m[32m              </Button>[m
[32m+[m[32m            </CardFooter>[m
[32m+[m[32m          </Card>[m
[32m+[m
           {/* Component Defaults Card */}[m
           <Card className="mb-6">[m
             <CardHeader>[m
