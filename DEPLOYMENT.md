# Deployment Guide for Hostinger VPS with Ubuntu

This guide will help you deploy the Web3Profiles application on a Hostinger VPS running Ubuntu.

## Prerequisites

- A Hostinger VPS with Ubuntu installed
- SSH access to your VPS
- Domain name (optional, but recommended)

## 1. Initial Server Setup

### Update and Upgrade Packages

```bash
sudo apt update
sudo apt upgrade -y
```

### Install Required Packages

```bash
sudo apt install -y curl wget git build-essential
```

## 2. Install Node.js

```bash
# Install Node.js 20.x (LTS)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node -v
npm -v
```

## 3. Install and Configure MySQL

```bash
# Install MySQL
sudo apt install -y mysql-server

# Start MySQL service
sudo systemctl start mysql
sudo systemctl enable mysql

# Secure the MySQL installation
sudo mysql_secure_installation
# Follow the prompts to set root password and secure your installation

# Log in to MySQL as root
sudo mysql -u root -p
# Enter your root password when prompted

# Create a database and user
mysql> CREATE DATABASE web3profiles CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
mysql> CREATE USER 'web3user'@'localhost' IDENTIFIED BY 'your_password';
mysql> GRANT ALL PRIVILEGES ON web3profiles.* TO 'web3user'@'localhost';
mysql> FLUSH PRIVILEGES;
mysql> EXIT;
```

### Configure MySQL for Remote Access (Optional)

If you need to access MySQL remotely (not recommended for production):

```bash
# Edit MySQL configuration
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

Find the line with `bind-address` and change it to:

```
bind-address = 0.0.0.0
```

```bash
# Restart MySQL
sudo systemctl restart mysql

# Create a user that can connect from any host
sudo mysql -u root -p
mysql> CREATE USER 'web3user'@'%' IDENTIFIED BY 'your_password';
mysql> GRANT ALL PRIVILEGES ON web3profiles.* TO 'web3user'@'%';
mysql> FLUSH PRIVILEGES;
mysql> EXIT;
```

## 4. Clone and Set Up the Application

```bash
# Create directory for the application
mkdir -p /var/www
cd /var/www

# Clone the repository
git clone https://your-repository-url.git web3profiles
cd web3profiles

# Install dependencies
npm install

# Create .env file
cp .env.example .env
nano .env
```

Edit the `.env` file with your production settings:

```
DATABASE_URL=mysql://web3user:your_password@localhost:3306/web3profiles
MYSQL_SSL=false
NEXT_PUBLIC_PROJECT_ID=your_reown_project_id
NODE_ENV=production
```

## 5. Set Up the Database

```bash
# Run database setup script
npm run setup:db

# Run migrations
npm run migrate
npm run migrate:images
```

## 6. Build and Start the Application

```bash
# Build the application
npm run build

# Install PM2 for process management
sudo npm install -g pm2

# Start the application with PM2
pm2 start npm --name "web3profiles" -- run start:prod

# Set PM2 to start on boot
pm2 startup
# Follow the instructions from the above command
pm2 save
```

## 7. Set Up Nginx as a Reverse Proxy

```bash
# Install Nginx
sudo apt install -y nginx

# Create Nginx configuration
sudo nano /etc/nginx/sites-available/web3profiles
```

Add the following configuration:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/web3profiles /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## 8. Set Up SSL with Let's Encrypt (Optional but Recommended)

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Follow the prompts to complete the setup
```

## 9. Maintenance and Updates

### Updating the Application

```bash
cd /var/www/web3profiles
git pull
npm install
npm run build
pm2 restart web3profiles
```

### Monitoring the Application

```bash
# View logs
pm2 logs web3profiles

# Monitor application
pm2 monit
```

### Database Backup

```bash
# Create a backup script
sudo nano /usr/local/bin/backup-db.sh
```

Add the following content:

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/mysql"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
mkdir -p $BACKUP_DIR
mysqldump -u web3user -p'your_password' web3profiles > $BACKUP_DIR/web3profiles_$TIMESTAMP.sql
```

```bash
# Make the script executable
sudo chmod +x /usr/local/bin/backup-db.sh

# Set up a cron job to run daily
sudo crontab -e
```

Add the following line:

```
0 2 * * * /usr/local/bin/backup-db.sh
```

## Troubleshooting

### Check Application Status

```bash
pm2 status
```

### Check Nginx Status

```bash
sudo systemctl status nginx
```

### Check MySQL Status

```bash
sudo systemctl status mysql
```

### View Application Logs

```bash
pm2 logs web3profiles
```

### View Nginx Logs

```bash
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```
