import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { componentPositions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// Define the type for the details object
interface BannerPfpDetails {
  profileNameHorizontalPosition?: number;
  profileShape?: string;
  profileHorizontalPosition?: number;
  profileNameStyle?: any;
  profileName?: string;
  profileBio?: string;
  backgroundColor?: string;
  fontColor?: string;
  [key: string]: any; // Allow other properties
}

type Context = {
  params: Promise<{
    address: string;
  }>;
};

// POST to update profile name horizontal position
export async function POST(
  request: NextRequest,
  context: Context
): Promise<Response> {
  try {
    const { address } = await context.params;
    const requestBody = await request.json();
    const { profileNameHorizontalPosition } = requestBody;

    console.log(`[profilename-position] Received request for address: ${address}`);
    console.log(`[profilename-position] Request body:`, requestBody);
    console.log(`[profilename-position] profileNameHorizontalPosition: ${profileNameHorizontalPosition}`);

    if (profileNameHorizontalPosition === undefined) {
      console.log(`[profilename-position] Error: profileNameHorizontalPosition is undefined`);
      return Response.json(
        { error: 'Profile name horizontal position is required' },
        { status: 400 }
      );
    }

    // Check if we already have a record for this address
    const existingRecord = await db.select().from(componentPositions).where(
      and(
        eq(componentPositions.address, address),
        eq(componentPositions.componentType, 'bannerpfp')
      )
    );

    if (existingRecord.length > 0) {
      // Get existing details and cast to the correct type
      const existingDetails: BannerPfpDetails = existingRecord[0].details || {};

      console.log(`[profilename-position] Found existing record for address: ${address}`);
      console.log(`[profilename-position] Existing details:`, existingDetails);
      console.log(`[profilename-position] Current profileNameHorizontalPosition: ${existingDetails.profileNameHorizontalPosition ?? 'not set'}`);
      console.log(`[profilename-position] New profileNameHorizontalPosition: ${profileNameHorizontalPosition}`);

      // Update existing record with typed details
      const updatedDetails: BannerPfpDetails = {
        ...existingDetails,
        profileNameHorizontalPosition
      };

      await db.update(componentPositions)
        .set({
          details: updatedDetails,
          updatedAt: new Date()
        })
        .where(
          and(
            eq(componentPositions.address, address),
            eq(componentPositions.componentType, 'bannerpfp')
          )
        );

      console.log(`[profilename-position] Updated record for address: ${address}`);
      console.log(`[profilename-position] New details:`, updatedDetails);

      return Response.json({ message: 'Profile name position updated successfully' });
    } else {
      return Response.json(
        { error: 'Bannerpfp component not found' },
        { status: 404 }
      );
    }
  } catch (error) {
    console.error('Failed to update profile name position:', error);
    return Response.json(
      { error: 'Failed to update profile name position' },
      { status: 500 }
    );
  }
}
