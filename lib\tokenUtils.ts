"use client";

import { formatUnits } from "viem";

// Default token address for Cronos chain
export const DEFAULT_TOKEN_ADDRESS = "0xe68892c424E4f0EDE343F6F05c873F7b7a528048";

// For backward compatibility
export const WEB3TOOLS_TOKEN_ADDRESS = DEFAULT_TOKEN_ADDRESS;

// Function to format token balance for display
export function formatTokenBalance(balance: bigint, decimals: number = 18): string {
  return formatUnits(balance, decimals);
}

// Function to convert string to bigint with proper decimal handling
export function parseTokenAmount(amount: string, decimals: number = 18): bigint {
  try {
    // Special case for '0'
    if (amount === '0') {
      return BigInt(0);
    }

    // Remove any commas or spaces
    const cleanAmount = amount.replace(/,|\s/g, '');

    // Split by decimal point
    const parts = cleanAmount.split('.');
    const wholePart = parts[0];
    const fractionalPart = parts[1] || '';

    // Pad or truncate fractional part to match decimals
    const paddedFractionalPart = fractionalPart.padEnd(decimals, '0').slice(0, decimals);

    // Combine whole and fractional parts without decimal point
    const combinedValue = wholePart + paddedFractionalPart;

    // Convert to bigint
    return BigInt(combinedValue);
  } catch (error) {
    console.error('Error parsing token amount:', error);
    return BigInt(0);
  }
}
