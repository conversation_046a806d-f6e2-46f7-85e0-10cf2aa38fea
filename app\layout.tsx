import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";
import SocialNavbar from "@/components/SocialNavbar";
import Providers from "@/components/Providers";
import ProfileStatusChecker from "@/components/ProfileStatusChecker";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Your Web 3 Profile",
  description: "Make your own web3 profile",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased overflow-x-hidden`}
      >
        <Providers>
          <ProfileStatusChecker />
          <div className="relative min-h-screen">
            <SocialNavbar />
            <div style={{ height: '48px' }}></div>
            {children}
          </div>
        </Providers>
        <Toaster />
      </body>
    </html>
  );
}

