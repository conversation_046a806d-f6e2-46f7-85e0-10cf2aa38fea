'use client';

import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { SolanaAdapter } from "@reown/appkit-adapter-solana";
import { cronos, solana, solanaTestnet, solanaDevnet } from "@reown/appkit/networks";
import { createAppKit } from "@reown/appkit/react";
import { createConfig, http } from 'wagmi';

export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

// Define networks for multichain support
export const networks = [
  cronos,
  solana,
  solanaTestnet,
  solanaDevnet,
];

// Create wagmi config (only for EVM chains)
export const wagmiConfig = createConfig({
  chains: [cronos],
  transports: {
    [cronos.id]: http(),
  },
});

// Create the Wagmi adapter for EVM chains
export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks: [cronos], // Only EVM networks for Wagmi
});

// Create the Solana adapter
export const solanaAdapter = new SolanaAdapter();

// Create multichain AppKit instance
createAppKit({
  adapters: [wagmiAdapter, solanaAdapter],
  networks,
  projectId,
  features: {
    swaps: false,
    onramp: false,
    email: true, // Enabled because Solana supports smart accounts
    socials: false,
    history: false,
    analytics: false,
    allWallets: false,
  },
});

export const config = wagmiConfig;
