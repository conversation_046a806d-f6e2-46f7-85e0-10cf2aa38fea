'use client';

import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { SolanaAdapter } from "@reown/appkit-adapter-solana/react";
import { cronos, solana, solanaTestnet, solanaDevnet } from "@reown/appkit/networks";
import { createAppKit } from "@reown/appkit/react";



export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

// Define EVM networks for Wagmi adapter
export const evmNetworks = [cronos];

// Define Solana networks
export const solanaNetworks = [solana, solanaTestnet, solanaDevnet];

// Define all networks for multichain support
export const networks = [
  ...evmNetworks,
  ...solanaNetworks,
] as any;

// Create the Wagmi adapter for EVM chains only
export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks: evmNetworks, // Only pass EVM networks to Wagmi
});

// Create the Solana adapter
export const solanaAdapter = new SolanaAdapter();

// Create wagmi config (for compatibility)
export const wagmiConfig = wagmiAdapter.wagmiConfig;

// Create multichain AppKit instance
createAppKit({
  adapters: [wagmiAdapter, solanaAdapter] as any, // Type assertion to bypass compatibility issue
  networks,
  projectId,
  features: {
    swaps: false,
    onramp: false,
    email: true, // Enabled because Solana supports smart accounts
    socials: false,
    history: false,
    analytics: false,
    allWallets: false,
  },
});

export const config = wagmiConfig;
