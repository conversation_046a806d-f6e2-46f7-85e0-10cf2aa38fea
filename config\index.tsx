'use client';

import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { SolanaAdapter } from "@reown/appkit-adapter-solana/react";
import { cronos, solana, solanaTestnet, solanaDevnet } from "@reown/appkit/networks";
import { createAppKit } from "@reown/appkit/react";

import type { AppKitNetwork } from '@reown/appkit/networks';

export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

// Define networks for multichain support
export const networks = [
  cronos,
  solana,
  solanaTestnet,
  solanaDevnet,
] as [AppKitNetwork, ...AppKitNetwork[]];

// Create the Wagmi adapter for EVM chains
export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks, // Pass all networks, adapter will filter EVM ones
});

// Create the Solana adapter with proper configuration
export const solanaAdapter = new SolanaAdapter({
  wallets: []
});

// Create wagmi config (for compatibility)
export const wagmiConfig = wagmiAdapter.wagmiConfig;

// Create multichain AppKit instance
createAppKit({
  adapters: [wagmiAdapter, solanaAdapter] as any, // Type assertion to bypass compatibility issue
  networks,
  projectId,
  features: {
    swaps: false,
    onramp: false,
    email: true, // Enabled because Solana supports smart accounts
    socials: false,
    history: false,
    analytics: false,
    allWallets: false,
  },
});

export const config = wagmiConfig;
