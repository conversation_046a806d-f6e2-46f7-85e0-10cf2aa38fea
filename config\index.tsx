'use client';

import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { SolanaAdapter } from "@reown/appkit-adapter-solana/react";
import { cronos, solana, solanaTestnet, solanaDevnet } from "@reown/appkit/networks";
import { createAppKit } from "@reown/appkit/react";

export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

// Define all networks for multichain support
export const networks = [
  cronos,
  solana,
  solanaTestnet,
  solanaDevnet,
] as any;

// Define custom RPC URLs for Solana networks
const customRpcUrls = {
  // Solana Mainnet
  'solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp': [
    { url: 'https://api.mainnet-beta.solana.com' }
  ],
  // Solana Testnet
  'solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3z': [
    { url: 'https://api.testnet.solana.com' }
  ],
  // Solana Devnet
  'solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1': [
    { url: 'https://api.devnet.solana.com' }
  ]
};

// Create the Wagmi adapter (pass all networks, it will filter EVM ones)
export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks, // Pass all networks, Wagmi will handle EVM ones
  customRpcUrls,
});

// Create the Solana adapter with proper RPC configuration
export const solanaAdapter = new SolanaAdapter({
  wallets: []
});

// Create wagmi config (for compatibility)
export const wagmiConfig = wagmiAdapter.wagmiConfig;

// Create multichain AppKit instance
createAppKit({
  adapters: [wagmiAdapter, solanaAdapter] as any, // Type assertion to bypass compatibility issue
  networks,
  projectId,
  customRpcUrls,
  features: {
    swaps: false,
    onramp: false,
    email: true, // Enabled because Solana supports smart accounts
    socials: false,
    history: false,
    analytics: false,
    allWallets: false,
  },
  // Add metadata to help with CORS and image loading
  metadata: {
    name: 'Web3 Socials',
    description: 'Web3 Social Profile Platform',
    url: typeof window !== 'undefined' ? window.location.origin : 'https://localhost:3000',
    icons: ['https://avatars.githubusercontent.com/u/*********']
  },
  // Disable features that might cause 403 errors
  enableWalletConnect: true,
  enableNetworkSwitch: true,
});

export const config = wagmiConfig;
