export function handleApiError(error: unknown): Response {
  console.error('API Error:', error);

  // Determine the error message
  let message = 'An unexpected error occurred';
  if (error instanceof Error) {
    message = error.message;
  } else if (typeof error === 'string') {
    message = error;
  }

  // Return a standardized error response
  return Response.json(
    {
      error: message,
      timestamp: new Date().toISOString()
    },
    {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        // Setting Cross-Origin-Opener-Policy to unsafe-none to be consistent with next.config.js
        'Cross-Origin-Opener-Policy': 'unsafe-none'
      }
    }
  );
}
